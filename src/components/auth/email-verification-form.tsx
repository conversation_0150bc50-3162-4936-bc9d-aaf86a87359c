"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Loader2, Mail } from "lucide-react"
import { verifyEmail, resendVerificationCode, type VerificationResponse } from "@/lib/auth-utils"
// import { useRouter } from 'next/navigation';

const verificationSchema = z.object({
  code: z.string().min(6, "Verification code must be at least 6 characters"),
})

type VerificationValues = z.infer<typeof verificationSchema>

interface EmailVerificationFormProps {
  email: string
  onSuccess?: (response: VerificationResponse) => void
  onError?: (error: string) => void
  onBack?: () => void
}

export function EmailVerificationForm({ 
  email, 
  onSuccess, 
  onError, 
  onBack 
}: EmailVerificationFormProps) {
  // const router = useRouter();
  const [isLoading, setIsLoading] = useState(false)
  const [isResending, setIsResending] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)

  const form = useForm<VerificationValues>({
    resolver: zodResolver(verificationSchema),
    defaultValues: {
      code: "",
    },
  })

  async function onSubmit(values: VerificationValues) {
    setIsLoading(true)
    setError(null)
    setSuccess(null)
    
    try {
      const result = await verifyEmail({
        email,
        code: values.code,
      })

      if (result.success) {
        setSuccess("Email verified successfully!")
        onSuccess?.(result)
        // router.push('/');
      } else {
        const errorMessage = result.error || "Verification failed"
        setError(errorMessage)
        onError?.(errorMessage)
      }
    } catch {
      const errorMessage = "An unexpected error occurred"
      setError(errorMessage)
      onError?.(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  async function handleResendCode() {
    setIsResending(true)
    setError(null)
    setSuccess(null)
    
    try {
      const result = await resendVerificationCode(email)
      
      if (result.success) {
        setSuccess("Verification code sent! Please check your email.")
      } else {
        setError(result.error || "Failed to resend code")
      }
    } catch {
      setError("An unexpected error occurred")
    } finally {
      setIsResending(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="text-center space-y-2">
        <div className="mx-auto w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
          <Mail className="w-6 h-6 text-primary" />
        </div>
        <h2 className="text-xl font-semibold">Verify your email</h2>
        <p className="text-sm text-muted-foreground">
          We&apos;ve sent a verification code to{" "}
          <span className="font-medium text-foreground">{email}</span>
        </p>
      </div>

      {error && (
        <div className="bg-destructive/15 text-destructive text-sm p-3 rounded-md">
          {error}
        </div>
      )}

      {success && (
        <div className="bg-green-500/15 text-green-500 text-sm p-3 rounded-md">
          {success}
        </div>
      )}

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <FormField
            control={form.control}
            name="code"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Verification Code</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Enter 6-digit code"
                    className="text-center text-lg tracking-widest"
                    maxLength={6}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <Button
            type="submit"
            className="w-full h-12 text-base"
            size="lg"
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Verifying...
              </>
            ) : (
              "Verify Email"
            )}
          </Button>
        </form>
      </Form>

      <div className="space-y-4">
        <div className="text-center">
          <p className="text-sm text-muted-foreground mb-2">
            Didn&apos;t receive the code?
          </p>
          <Button
            type="button"
            variant="outline"
            onClick={handleResendCode}
            disabled={isResending}
            className="w-full"
          >
            {isResending ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Sending...
              </>
            ) : (
              "Resend Code"
            )}
          </Button>
        </div>

        {onBack && (
          <Button
            type="button"
            variant="ghost"
            onClick={onBack}
            className="w-full"
          >
            Back to Signup
          </Button>
        )}
      </div>

      <div className="text-xs text-muted-foreground text-center">
        <p>Check your spam folder if you don&apos;t see the email.</p>
        <p>The verification code expires in 24 hours.</p>
      </div>
    </div>
  )
}
