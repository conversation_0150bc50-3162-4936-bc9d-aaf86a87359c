"use client"

import { Play } from "lucide-react"
import { useMusicPlayer, type Song } from "@/contexts/music-player-context/music-player-context"
import Image from "next/image"

interface SongRowProps {
  song: Song
  index: number
}

export function SongRow({ song, index }: SongRowProps) {
  const { currentSong } = useMusicPlayer()

  const isCurrentSong = currentSong?.id === song.id

  return (
    <div
      className="grid grid-cols-12 gap-4 px-4 py-3 rounded-lg hover:bg-muted/50 group cursor-not-allowed opacity-50"
    >
      <div className="col-span-1 flex items-center">
        <span className="text-muted-foreground">{index + 1}</span>
        <Play className="w-4 h-4 hidden opacity-50" />
      </div>

      <div className="col-span-5 flex items-center gap-3">
        <Image src={song.albumArt || "/placeholder.svg"} alt={song.album} width={40} height={40} className="rounded" />
        <div>
          <p className={`font-medium ${isCurrentSong ? "text-primary" : ""}`}>{song.title}</p>
          <p className="text-sm text-muted-foreground">{song.artist}</p>
        </div>
      </div>

      <div className="col-span-4 flex items-center">
        <span className="text-sm text-muted-foreground">{song.album}</span>
      </div>

      <div className="col-span-2 flex items-center">
        <span className="text-sm text-muted-foreground">
          {Math.floor(song.duration / 60)}:{(song.duration % 60).toString().padStart(2, "0")}
        </span>
      </div>
    </div>
  )
}
