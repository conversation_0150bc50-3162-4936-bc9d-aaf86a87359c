"use client"

import { useEffect, useRef, useCallback } from 'react'

interface UseIntersectionObserverOptions {
  onIntersect: () => void
  threshold?: number
  rootMargin?: string
  enabled?: boolean
}

export function useIntersectionObserver({
  onIntersect,
  threshold = 0.1,
  rootMargin = '100px',
  enabled = true
}: UseIntersectionObserverOptions) {
  const targetRef = useRef<HTMLDivElement>(null)

  const handleIntersect = useCallback(
    (entries: IntersectionObserverEntry[]) => {
      const [entry] = entries
      if (entry.isIntersecting && enabled) {
        onIntersect()
      }
    },
    [onIntersect, enabled]
  )

  useEffect(() => {
    const target = targetRef.current
    if (!target || !enabled) return

    const observer = new IntersectionObserver(handleIntersect, {
      threshold,
      rootMargin
    })

    observer.observe(target)

    return () => {
      observer.unobserve(target)
      observer.disconnect()
    }
  }, [handleIntersect, threshold, rootMargin, enabled])

  return targetRef
}
