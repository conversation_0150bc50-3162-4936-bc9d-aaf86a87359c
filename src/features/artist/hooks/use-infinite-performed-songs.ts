"use client"

import { useState, useCallback } from 'react'
import { ApolloError, useQuery } from '@apollo/client'
import { PERFORMED_SONGS_QUERY } from '@/graphql/queries'

export interface PerformedSong {
  id: string
  title?: string | null
  duration?: number | null
  recordID?: string | null
  releaseDate?: string | null
  coverPhoto?: string | null
  type: 'recording'
}

interface UseInfinitePerformedSongsReturn {
  performedSongs: PerformedSong[]
  loading: boolean
  error: ApolloError | undefined
  hasNextPage: boolean
  loadMore: () => void
  totalCount: number
  isLoadingMore: boolean
}

const ITEMS_PER_PAGE = 20

export function useInfinitePerformedSongs(artistId: string): UseInfinitePerformedSongsReturn {
  const [isLoadingMore, setIsLoadingMore] = useState(false)

  // Query for performed songs data (following ARTISTS_QUERY pattern)
  const { data, loading, error, fetchMore } = useQuery(PERFORMED_SONGS_QUERY, {
    variables: {
      where: { mbid: artistId },
      first: ITEMS_PER_PAGE,
      after: null,
    },
    skip: !artistId,
    notifyOnNetworkStatusChange: true,
    onError: (error) => {
      console.error("GraphQL Error in performed songs:", error)
      console.error("Artist ID:", artistId)
    },
    onCompleted: (data) => {
      console.log("Performed songs loaded for artist:", artistId, "- Found:", data?.artistsConnection?.edges?.[0]?.node?.performedConnection?.totalCount || 0, "songs")
    }
  })

  // Process Apollo data from connection structure (following use-infinite-artists pattern)
  const processPerformedSongsData = useCallback((apolloData: typeof data): PerformedSong[] => {
    if (!apolloData?.artistsConnection?.edges?.[0]?.node?.performedConnection?.edges) {
      console.warn("No performed songs data received from Apollo")
      return []
    }

    const performedEdges = apolloData.artistsConnection.edges[0].node.performedConnection.edges

    const processedSongs = performedEdges
      .filter((edge: unknown) => {
        if (typeof edge === 'object' && edge !== null) {
          const e = edge as Record<string, unknown>
          const node = e.node as Record<string, unknown>
          return node && typeof node.mbid === 'string'
        }
        return false
      })
      .map((edge: unknown) => {
        const e = edge as Record<string, unknown>
        const node = e.node as Record<string, unknown>
        return {
          id: String(node.mbid), // Use mbid as id
          title: node.title ? String(node.title) : null,
          duration: node.duration ? Number(node.duration) : null,
          recordID: node.recordID ? String(node.recordID) : null,
          releaseDate: node.releaseDate ? String(node.releaseDate) : null,
          coverPhoto: node.coverImage ? String(node.coverImage) : null,
          type: 'recording' as const,
        } as PerformedSong
      })

    return processedSongs
  }, [])

  // Get processed performed songs directly from Apollo cache (following use-infinite-artists pattern)
  const performedSongs = data?.artistsConnection?.edges?.[0]?.node?.performedConnection ? processPerformedSongsData(data) : []
  const totalCount = data?.artistsConnection?.edges?.[0]?.node?.performedConnection?.totalCount || 0
  const hasNextPage = data?.artistsConnection?.edges?.[0]?.node?.performedConnection?.pageInfo?.hasNextPage || false
  const endCursor = data?.artistsConnection?.edges?.[0]?.node?.performedConnection?.pageInfo?.endCursor || null

  // Debug logging
  console.log("Performed songs hook state:", {
    artistId,
    performedSongsCount: performedSongs.length,
    totalCount,
    hasNextPage,
    endCursor,
    loading,
    isLoadingMore,
    error: error?.message
  })

  // Load more function - GraphQL pagination (following use-infinite-artists pattern)
  const loadMore = useCallback(async () => {
    if (!hasNextPage || isLoadingMore || loading) {
      console.log("Load more blocked:", { hasNextPage, isLoadingMore, loading })
      return
    }

    console.log("Loading more performed songs with cursor:", endCursor)
    setIsLoadingMore(true)
    try {
      await fetchMore({
        variables: {
          where: { mbid: artistId },
          first: ITEMS_PER_PAGE,
          after: endCursor,
        },
        updateQuery: (prev, { fetchMoreResult }) => {
          console.log("UpdateQuery called:", { prev, fetchMoreResult })

          if (!fetchMoreResult) {
            console.log("No fetchMoreResult")
            return prev
          }

          const prevPerformed = prev?.artistsConnection?.edges?.[0]?.node?.performedConnection
          const newPerformed = fetchMoreResult?.artistsConnection?.edges?.[0]?.node?.performedConnection

          console.log("Performed connections:", { prevPerformed, newPerformed })

          if (!prevPerformed || !newPerformed) {
            console.log("Missing performed connections")
            return prev
          }

          // Merge the edges
          const mergedEdges = [...(prevPerformed.edges || []), ...(newPerformed.edges || [])]
          console.log("Merged edges count:", mergedEdges.length, "Previous:", prevPerformed.edges?.length, "New:", newPerformed.edges?.length)

          const result = {
            ...prev,
            artistsConnection: {
              ...prev.artistsConnection,
              edges: [
                {
                  ...prev.artistsConnection.edges[0],
                  node: {
                    ...prev.artistsConnection.edges[0].node,
                    performedConnection: {
                      ...newPerformed,
                      edges: mergedEdges,
                    },
                  },
                },
              ],
            },
          }

          console.log("UpdateQuery result:", result)
          return result
        },
      })

      console.log("Loaded more performed songs successfully")
    } catch (err) {
      console.error("Error loading more performed songs:", err)
    } finally {
      setIsLoadingMore(false)
    }
  }, [hasNextPage, isLoadingMore, loading, endCursor, fetchMore, artistId])

  return {
    performedSongs,
    loading,
    error,
    hasNextPage,
    loadMore,
    totalCount,
    isLoadingMore,
  }
}
