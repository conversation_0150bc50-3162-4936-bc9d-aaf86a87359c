"use client"

import { useState, useCallback } from 'react'
import { ApolloError, useQuery } from '@apollo/client'
import { ARTISTS_CONNECTION_ALL } from '@/graphql/queries'
import { Artist } from '../types/artist'

interface UseAllArtistsReturn {
  artists: Artist[]
  loading: boolean
  error: ApolloError | undefined
  totalCount: number
  searchArtists: (searchTerm: string) => void
  clearSearch: () => void
  filteredArtists: Artist[]
}

export function useAllArtists(): UseAllArtistsReturn {
  const [searchTerm, setSearchTerm] = useState<string>("")

  // Query for all artists data without pagination
  const { data, loading, error } = useQuery(ARTISTS_CONNECTION_ALL, {
    notifyOnNetworkStatusChange: true,
    onError: (error) => {
      console.error("GraphQL Error:", error)
    },
    onCompleted: (data) => {
      console.log("All artists loaded:", data?.artistsConnection?.totalCount || 0, "results")
    }
  })

  // Process Apollo data from connection structure to match Artist interface
  const processApolloArtistData = useCallback((apolloData: typeof data): Artist[] => {
    if (!apolloData?.artistsConnection?.edges) {
      console.warn("No artist data received from Apollo")
      return []
    }

    const processedArtists = apolloData.artistsConnection.edges
      .filter((edge: unknown) => {
        if (typeof edge === 'object' && edge !== null) {
          const e = edge as Record<string, unknown>
          const node = e.node as Record<string, unknown>
          return node && typeof node.mbid === 'string' && (typeof node.name === 'string' || typeof node.artistName === 'string')
        }
        return false
      })
      .map((edge: unknown) => {
        const e = edge as Record<string, unknown>
        const node = e.node as Record<string, unknown>
        return {
          __typename: "Artist",
          id: String(node.mbid), // Use mbid as id
          name: String(node.name || node.artistName),
          bio: node.bio ? String(node.bio) : null,
          formedDate: node.formedDate ? String(node.formedDate) : null,
          disbandedDate: node.disbandedDate ? String(node.disbandedDate) : null,
          location: null, // Not available in Apollo data
          profileImage: node.profileImage ? String(node.profileImage) : null,
          gender: node.gender ? String(node.gender) : null,
          links: node.links ? String(node.links) : null,
          genres: node.genres ? (Array.isArray(node.genres) ? node.genres.map(String) : [String(node.genres)]) : null,
        } as Artist
      })

    return processedArtists
  }, [])

  // Get processed artists directly from Apollo cache
  const artists = data?.artistsConnection ? processApolloArtistData(data) : []
  const totalCount = data?.artistsConnection?.totalCount || 0

  // Client-side filtering for search
  const filteredArtists = searchTerm.trim() 
    ? artists.filter(artist => {
        const term = searchTerm.toLowerCase()
        return (
          artist.name.toLowerCase().includes(term) ||
          (artist.bio && artist.bio.toLowerCase().includes(term)) ||
          (artist.gender && artist.gender.toLowerCase().includes(term)) ||
          (artist.genres && artist.genres.some(genre => genre.toLowerCase().includes(term)))
        )
      })
    : artists

  // Search function
  const searchArtists = useCallback((searchTerm: string) => {
    setSearchTerm(searchTerm)
  }, [])

  // Clear search function
  const clearSearch = useCallback(() => {
    setSearchTerm("")
  }, [])

  return {
    artists,
    loading,
    error,
    totalCount,
    searchArtists,
    clearSearch,
    filteredArtists
  }
}
