"use client"

import { useState, useCallback } from "react"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { Search, Users, Filter } from "lucide-react"
import { ArtistCard } from "./artist-card"
import { Skeleton } from "@/components/ui/skeleton"
import { useRouter } from "next/navigation"
// Old pagination-based imports (commented out)
// import { useInfiniteArtists } from "../hooks/use-infinite-artists"
// import { useIntersectionObserver } from "../hooks/use-intersection-observer"

// New import for fetching all artists without pagination
import { useAllArtists } from "../hooks/use-all-artists"


export default function ArtistsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const router = useRouter()

  // Old infinite scroll hook (commented out)
  // const {
  //   artists,
  //   loading,
  //   error,
  //   hasNextPage,
  //   loadMore,
  //   totalCount,
  //   isLoadingMore,
  //   searchArtists,
  //   clearSearch
  // } = useInfiniteArtists();

  // New hook for fetching all artists without pagination
  const {
    artists,
    loading,
    error,
    totalCount,
    searchArtists,
    clearSearch,
    filteredArtists
  } = useAllArtists();

  // Handle search input changes
  const handleSearchChange = useCallback((value: string) => {
    setSearchTerm(value)
    if (value.trim()) {
      searchArtists(value.trim())
    } else {
      clearSearch()
    }
  }, [searchArtists, clearSearch])

  // Old client-side filtering (commented out - now handled by the hook)
  // const filteredArtists = artists

  // Old intersection observer for infinite scroll (commented out - no longer needed)
  // const loadMoreRef = useIntersectionObserver({
  //   onIntersect: loadMore,
  //   enabled: hasNextPage && !isLoadingMore && !loading
  // })

  return (
    <div className="flex flex-col h-full w-full overflow-hidden">
      <div className="flex flex-col h-full p-2">

        {/* Search and Filters */}
        <Card className="mb-4 flex-shrink-0">
          <CardContent className="pt-4 pb-4">
            <div className="flex flex-col lg:flex-row gap-4">
              {/* Search */}
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                  <Input
                    placeholder="Search artists by name or bio..."
                    value={searchTerm}
                    onChange={(e) => handleSearchChange(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
            </div>

            {/* Search Summary */}
            <div className="flex items-center gap-4 mt-3 text-sm text-muted-foreground">
              <div className="flex items-center gap-1">
                <Filter className="w-4 h-4" />
                <span>
                  {searchTerm ? `Found ${filteredArtists.length} artists` : `Showing ${totalCount} artists`}
                </span>
              </div>
              {searchTerm && <span>• Searching for &ldquo;{searchTerm}&rdquo;</span>}
            </div>
          </CardContent>
        </Card>

        {/* Artists Grid */}
        <div className="flex-1 min-h-0 overflow-auto">
          {loading && artists.length === 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 h-full overflow-y-auto pr-2">
              {Array.from({ length: 8 }).map((_, i) => (
                <Card key={i} className="h-fit">
                  <CardContent className="pt-4 pb-4">
                    <Skeleton className="h-24 w-full mb-3" />
                    <Skeleton className="h-5 w-3/4 mb-2" />
                    <Skeleton className="h-4 w-1/2" />
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : error ? (
            <div className="flex items-center justify-center h-full">
              <Card className="text-center py-8">
                <CardContent>
                  <Users className="w-10 h-10 text-destructive mx-auto mb-3" />
                  <h3 className="text-lg font-semibold mb-2">Error loading artists</h3>
                  <p className="text-muted-foreground text-sm">
                    {error.message || "Failed to fetch artists. Please try again later."}
                  </p>
                </CardContent>
              </Card>
            </div>
          ) : filteredArtists.length > 0 ? (
            <div className="h-full overflow-y-auto pr-2">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                {filteredArtists.map((artist) => (
                  <div key={artist.id} onClick={() => router.push(`/artist/?id=${artist.id}`)} className="cursor-pointer h-fit">
                    <ArtistCard artist={artist} />
                  </div>
                ))}
              </div>

              {/* Old infinite scroll section (commented out - no longer needed) */}
              {/* {hasNextPage && (
                <div ref={loadMoreRef} className="flex justify-center py-8">
                  {isLoadingMore ? (
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <Loader2 className="w-4 h-4 animate-spin" />
                      <span>Loading more artists...</span>
                    </div>
                  ) : (
                    <div className="text-muted-foreground text-sm">
                      Scroll down to load more artists
                    </div>
                  )}
                </div>
              )} */}
            </div>
          ) : (
            <div className="flex items-center justify-center h-full">
              <Card className="text-center py-8">
                <CardContent>
                  <Users className="w-10 h-10 text-muted-foreground mx-auto mb-3" />
                  <h3 className="text-lg font-semibold mb-2">No artists found</h3>
                  <p className="text-muted-foreground text-sm">
                    {searchTerm
                      ? "Try adjusting your search criteria."
                      : "No artists available at the moment."}
                  </p>
                </CardContent>
              </Card>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
