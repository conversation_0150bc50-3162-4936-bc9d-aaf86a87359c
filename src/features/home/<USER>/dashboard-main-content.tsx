import { Card } from "@/components/ui/card";

export default function DashboardMainContent() {
  return (
    <section className="flex-1 px-8 py-8">
      {/* My Active Projects */}
      <div className="flex items-center justify-between mb-6">
        {/* <h2 className="text-2xl font-bold">My Active Projects</h2>
        <a href="#" className="text-sm text-primary hover:underline">Show all</a> */}
      </div>
    
      {/* My Tasks */}
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-bold">My Tasks</h2>
        <a href="#" className="text-sm text-primary hover:underline">Show all</a>
      </div>
      <div className="grid md:grid-cols-2 gap-4 mb-10">
        {/* Example tasks (replace with real data) */}
        {[1,2].map((_,i) => (
          <Card key={i} className="p-4 flex flex-col gap-2">
            <div className="flex items-center justify-between">
              <div>
                <div className="font-semibold">Task Name</div>
                <div className="text-xs text-muted-foreground">Mix Ready for your review</div>
              </div>
              <button className="bg-primary text-white rounded px-4 py-1 text-xs font-semibold">Review</button>
            </div>
            <div className="text-xs text-muted-foreground">Sarah Anderson · 2 hours ago</div>
          </Card>
        ))}
      </div>
      {/* Recommended Gigs */}
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-bold">Recommended Gigs</h2>
        <a href="#" className="text-sm text-primary hover:underline">Show all</a>
      </div>
      <div className="grid md:grid-cols-2 gap-4">
        {/* Example gigs (replace with real data) */}
        {[1,2].map((_,i) => (
          <Card key={i} className="p-4 flex flex-col gap-2">
            <div className="font-semibold">Gig Name</div>
            <div className="text-xs text-muted-foreground">A short description of the gig opportunity.</div>
          </Card>
        ))}
      </div>
    </section>
  );
}
