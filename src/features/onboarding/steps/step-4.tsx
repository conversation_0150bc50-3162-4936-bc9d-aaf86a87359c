import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useOnboarding } from '@/features/onboarding/onboarding-context';

// Main roles and their sub-roles
const ROLE_OPTIONS = [
	{
		role: 'Songwriter/Composer',
		subRoles: [
			'Topline Writer',
			'Lyricist',
			'Arranger',
			'Melodywriter',
			'Orchestrator/Conductor',
      'Remixer '
		],
	},
	{
		role: 'Producer',
		subRoles: [
			'Remixer',
			'Beatmaker/Trackmaker',
			'Arranger',
		],
	},
	{
		role: 'Musician/Instrumentalist',
		subRoles: [
			'Guitarist',
			'Drummer',
			'Bassist',
			'Other',
		],
	},
	{
		role: 'Engineer/Editor',
		subRoles: [
			'Mix Engineer',
			'Mastering Engineer',
			'Editor',
		],
	},
	{
		role: 'Artist/Performer',
		subRoles: [
			'Vocalist',
			'Instrumentalist',
			'Other',
		],
	},
];

export default function Step4() {
	const { step, setStep, data, setData } = useOnboarding();
	const selectedRole = data.roles?.[0] || '';
	const selectedSubRoles = data.subRoles || [];

	// Select only one main role
	const handleRoleSelect = (role: string) => {
		setData({ roles: [role], subRoles: [] });
	};

	// Toggle sub-role selection
	const handleSubRoleToggle = (subRole: string) => {
		setData({
			subRoles: selectedSubRoles.includes(subRole)
				? selectedSubRoles.filter(r => r !== subRole)
				: [...selectedSubRoles, subRole],
		});
	};

	// Find sub-roles for selected main role
	const currentRoleObj = ROLE_OPTIONS.find(r => r.role === selectedRole);

	return (
		<div className="flex flex-col w-full max-w-md mx-auto">
			<h1 className="text-2xl font-bold mb-4 mt-2">Pick roles that fit you</h1>
			<div className="text-sm text-muted-foreground mb-4">
				Select one role that matches your vibe
			</div>
			<div className="flex w-full flex-wrap gap-2 mb-8">
				{ROLE_OPTIONS.map(({ role }) => (
					<Badge
						key={role}
						variant={selectedRole === role ? 'default' : 'outline'}
						className={`cursor-pointer text-center py-2 px-4 text-sm font-medium rounded transition select-none
              ${selectedRole === role
								? 'bg-primary text-primary-foreground border-primary'
								: 'bg-card text-foreground border-border hover:border-primary'}`}
						onClick={() => handleRoleSelect(role)}
					>
						{role}
					</Badge>
				))}
			</div>
			{selectedRole && currentRoleObj && (
				<div className="bg-muted rounded-lg p-4 mb-10">
					<div className="font-semibold mb-3 text-foreground">Select the sub-roles that match</div>
					<div className="flex flex-wrap gap-2">
						{currentRoleObj.subRoles.map(subRole => (
							<Badge
								key={subRole}
								variant={selectedSubRoles.includes(subRole) ? 'default' : 'outline'}
								className={`cursor-pointer text-center py-2 px-4 text-sm font-medium rounded transition select-none
                  ${selectedSubRoles.includes(subRole)
											? 'bg-primary text-primary-foreground border-primary'
											: 'bg-card text-foreground border-border hover:border-primary'}`}
								onClick={() => handleSubRoleToggle(subRole)}
							>
								{subRole}
							</Badge>
						))}
					</div>
				</div>
			)}
			<div className="flex w-full justify-between gap-2">
				<Button
					variant="outline"
					className="w-32"
					onClick={() => setStep(step - 1)}
				>
					Back
				</Button>
				<Button
					className="w-32"
					onClick={() => setStep(step + 1)}
					disabled={!selectedRole || selectedSubRoles.length === 0}
				>
					Continue
				</Button>
			</div>
		</div>
	);
}
