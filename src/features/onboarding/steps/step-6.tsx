import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { useOnboarding } from '@/features/onboarding/onboarding-context';

const radioOptions = [
	{ value: 'search', label: 'Search & claim existing credits' },
	{ value: 'url', label: 'Paste your artist profile URL' },
];

export default function Step6() {
	const { step, setStep, data, setData } = useOnboarding();
	const type = data.artistProfileType || 'search';
	const search = data.artistProfileSearch || '';
	const url = data.artistProfileUrl || '';
	const noProfile = !!data.noArtistProfile;

	const canContinue =
		noProfile || (type === 'search' && search.trim()) || (type === 'url' && url.trim());

	return (
		<div className="flex flex-col w-full max-w-md mx-auto">
			<h1 className="text-2xl font-bold mb-4 mt-2">Link your artist profile</h1>
			<div className="text-sm text-muted-foreground mb-4">
				Your artist profile will be essential for understanding who you are and who your
				audience is. Please make sure you are choosing the right profile.
			</div>
			<div className="flex flex-col gap-4 mb-4">
				<RadioGroup
					value={type}
					onValueChange={val =>
						setData({
							artistProfileType: val as 'search' | 'url',
							noArtistProfile: false,
						})
					}
				>
					{radioOptions.map(opt => (
						<Label
							key={opt.value}
							className="flex items-center gap-3 cursor-pointer text-foreground"
						>
							<RadioGroupItem value={opt.value} id={opt.value} />
							<span className="font-medium">{opt.label}</span>
						</Label>
					))}
				</RadioGroup>
				{type === 'search' && (
					<Input
						placeholder="Search artist name, band or track"
						value={search}
						onChange={e => setData({ artistProfileSearch: e.target.value })}
						className="mt-1 bg-card text-foreground border border-input placeholder-muted-foreground"
					/>
				)}
				{type === 'url' && (
					<Input
						placeholder="Paste your artist profile URL"
						value={url}
						onChange={e => setData({ artistProfileUrl: e.target.value })}
						className="mt-1 bg-card text-foreground border border-input placeholder-muted-foreground"
					/>
				)}
				<Label className="flex items-center gap-2 mt-2 cursor-pointer text-foreground">
					<Checkbox
						checked={noProfile}
						onCheckedChange={checked => setData({ noArtistProfile: !!checked })}
					/>
					<span className="font-medium">I don’t have an artist profile</span>
				</Label>
			</div>
			<div className="flex w-full justify-between gap-2 mt-6">
				<Button
					variant="outline"
					className="w-32"
					onClick={() => setStep(step - 1)}
				>
					Back
				</Button>
				<Button
					className="w-32"
					onClick={() => setStep(step + 1)}
					disabled={!canContinue}
				>
					Continue
				</Button>
			</div>
		</div>
	);
}
