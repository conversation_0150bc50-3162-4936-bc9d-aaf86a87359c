import React, { useRef } from 'react';
import { Button } from '@/components/ui/button';
import { useOnboarding } from '@/features/onboarding/onboarding-context';

export default function Step2() {
  const { step, setStep, data, setData } = useOnboarding();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const profilePhoto = data.profilePhoto || null;

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setData({ profilePhoto: file });
    }
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    const file = e.dataTransfer.files?.[0];
    if (file) {
      setData({ profilePhoto: file });
    }
  };

  return (
    <div className="flex flex-col w-full max-w-md mx-auto">
      <h1 className="text-2xl font-bold mb-4 mt-2">Choose a profile photo</h1>
      <div
        className="border-2 border-dashed rounded-xl flex flex-col items-center justify-center py-10 mb-10 bg-card border-input hover:border-primary transition cursor-pointer"
        onClick={() => fileInputRef.current?.click()}
        onDrop={handleDrop}
        onDragOver={e => e.preventDefault()}
      >
        <div className="text-base font-semibold mb-1 text-foreground">Add a profile photo</div>
        <div className="text-xs text-muted-foreground mb-4">Drag and drop or click to upload</div>
        <Button
          type="button"
          variant="secondary"
          className="rounded-full px-6 py-2 text-base font-medium bg-muted text-foreground"
          onClick={e => { e.stopPropagation(); fileInputRef.current?.click(); }}
        >
          Upload a photo
        </Button>
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          className="hidden"
          onChange={handleFileChange}
        />
        {profilePhoto && (
          <div className="mt-4 text-xs text-green-600 dark:text-green-400">{profilePhoto.name}</div>
        )}
      </div>
      <div className="flex w-full justify-between gap-2">
        <Button variant="outline" className="w-32" onClick={() => setStep(step - 1)}>Back</Button>
        <Button className="w-32" onClick={() => setStep(step + 1)} disabled={!profilePhoto}>Continue</Button>
      </div>
    </div>
  );
}
