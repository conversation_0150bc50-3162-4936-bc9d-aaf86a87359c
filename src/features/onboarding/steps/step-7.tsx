import React from 'react';
import { Button } from '@/components/ui/button';
import { useOnboarding } from '@/features/onboarding/onboarding-context';

export default function Step7() {
  const { step, setStep, data, setData } = useOnboarding();

  return (
    <div className="flex flex-col w-full max-w-md mx-auto">
      <h1 className="text-2xl font-bold mb-4 mt-2">Add your social links</h1>
      <div className="text-sm text-muted-foreground mb-4">
        Connect your music and social media profiles to showcase your work and expand your network.
      </div>
      <form className="flex flex-col gap-4 mb-10">
        <div>
          <label className="block text-sm font-medium mb-1">Spotify link</label>
          <input
            type="text"
            className="w-full border rounded px-3 py-2"
            placeholder="Paste your Spotify profile link"
            value={data.spotifyLink || ''}
            onChange={e => setData({ spotifyLink: e.target.value })}
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">Apple Music link</label>
          <input
            type="text"
            className="w-full border rounded px-3 py-2"
            placeholder="Paste your Apple Music profile link"
            value={data.appleMusicLink || ''}
            onChange={e => setData({ appleMusicLink: e.target.value })}
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">YouTube link</label>
          <input
            type="text"
            className="w-full border rounded px-3 py-2"
            placeholder="Paste your YouTube profile link"
            value={data.youtubeLink || ''}
            onChange={e => setData({ youtubeLink: e.target.value })}
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">Instagram link</label>
          <input
            type="text"
            className="w-full border rounded px-3 py-2"
            placeholder="Paste your Instagram profile link"
            value={data.instagramLink || ''}
            onChange={e => setData({ instagramLink: e.target.value })}
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">Linkedin link</label>
          <input
            type="text"
            className="w-full border rounded px-3 py-2"
            placeholder="Paste your Linkedin profile link"
            value={data.linkedinLink || ''}
            onChange={e => setData({ linkedinLink: e.target.value })}
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">Other link (if any)</label>
          <input
            type="text"
            className="w-full border rounded px-3 py-2"
            placeholder="Paste other social profile links(if any)"
            value={data.otherLink || ''}
            onChange={e => setData({ otherLink: e.target.value })}
          />
        </div>
      </form>
      <div className="flex w-full justify-between gap-2">
        <Button variant="outline" className="w-32" onClick={() => setStep(step - 1)}>
          Back
        </Button>
        <Button className="w-32" onClick={() => setStep(step + 1)}>
          Continue
        </Button>
      </div>
    </div>
  );
}