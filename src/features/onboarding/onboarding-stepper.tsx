import React from 'react';
import { useOnboarding } from './onboarding-context';

const steps = [1, 2, 3, 4, 5, 6, 7, 8]; // Updated for 8 steps

export function OnboardingStepper() {
  const { step } = useOnboarding();
  return (
    <div className="flex gap-2 mb-6">
      {steps.map((s) => (
        <div
          key={s}
          className={`h-1 w-10 rounded-full transition-colors duration-200 ${step >= s ? 'bg-primary' : 'bg-muted'}`}
        />
      ))}
    </div>
  );
}
