/* eslint-disable react/no-unescaped-entities */
import type React from "react"
import { Card } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import {
  Heart,
  MessageCircle,
  Share,
  PlayCircle,
  Bookmark,
  MoreHorizontal,
  MapPin,
  Calendar,
  DollarSign,
} from "lucide-react"

interface FeedCardProps {
  userName: string
  userRole: string
  time: string
  postContent: React.ReactNode | string
  badges?: React.ReactNode
  stats: {
    likes: number
    comments: number
  }
  children?: React.ReactNode
  type?: string
}

export default function FeedCard({
  userName,
  userRole,
  time,
  postContent,
  badges,
  stats,
  children,
  type,
}: FeedCardProps) {
  return (
    <Card className="p-6 bg-card border-0 shadow-sm rounded-lg">
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center gap-3">
          <Avatar className="w-12 h-12">
            <AvatarFallback className="bg-muted-foreground text-primary-foreground font-medium">
              {userName
                .split(" ")
                .map((n) => n[0])
                .join("")}
            </AvatarFallback>
          </Avatar>
          <div>
            <div className="font-semibold text-foreground text-base">{userName}</div>
            <div className="text-sm text-muted-foreground">{userRole}</div>
          </div>
          <div className="text-sm text-muted-foreground">{time}</div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="sm" className="text-muted-foreground hover:text-foreground p-2">
            <Bookmark className="w-5 h-5" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="bg-card text-foreground border-border hover:bg-accent px-4 py-1.5 text-sm font-medium"
          >
            Follow
          </Button>
          <Button variant="ghost" size="sm" className="text-muted-foreground hover:text-foreground p-2">
            <MoreHorizontal className="w-5 h-5" />
          </Button>
        </div>
      </div>

      {/* Content */}
      <div className="mb-4">
        {type === "opportunity" ? (
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <span className="font-semibold text-foreground text-base">Music Producer Needed</span>
              <Badge variant="secondary" className="bg-secondary text-foreground/80 text-xs px-2 py-1">
                Opportunity
              </Badge>
            </div>
            <div className="text-sm text-muted-foreground">Project: Musically</div>
            <div className="text-foreground text-base leading-relaxed">
              We're looking for a talented music producer to join our team. Experience with electronic music production
              and modern DAWs required. This is a remote position with flexible hours.
            </div>
            <div className="text-sm text-blue-600">#MusicProduction #StudioLife #Electronic #Remote #DAW</div>
            <div className="flex items-center gap-6 text-sm text-foreground/80">
              <div className="flex items-center gap-1">
                <MapPin className="w-4 h-4" />
                <Badge variant="outline" className="bg-card text-foreground/80 border-border text-xs px-2 py-1">
                  Remote
                </Badge>
              </div>
              <div className="flex items-center gap-1">
                <Calendar className="w-4 h-4" />
                <span>less than a month</span>
              </div>
              <div className="flex items-center gap-1">
                <DollarSign className="w-4 h-4" />
                <span>$1200</span>
              </div>
            </div>
          </div>
        ) : (
          <div className="text-foreground text-base leading-relaxed">{postContent}</div>
        )}
        {badges && <div className="mt-3">{badges}</div>}
      </div>

      {/* Children Content (Music Player, Link Preview, Playlist, etc.) */}
      {children && (
        <div className="mb-4">
          {type === "music" ? (
            <Card className="p-4 bg-muted border-0 rounded-lg">
              <div className="flex items-center gap-4">
                <div className="w-16 h-16 bg-muted-foreground rounded-lg flex items-center justify-center">
                  <PlayCircle className="w-8 h-8 text-primary-foreground" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="font-semibold text-foreground">Cassanova</span>
                    <Badge variant="secondary" className="bg-secondary text-foreground/80 text-xs px-2 py-0.5">
                      Dance & EDM
                    </Badge>
                  </div>
                  <div className="text-sm text-muted-foreground mb-2">Neal Altmayer • 2:30</div>
                  <div className="w-full h-1 bg-border rounded-full">
                    <div className="h-full bg-foreground/60 rounded-full" style={{ width: "40%" }} />
                  </div>
                </div>
              </div>
            </Card>
          ) : type === "link" ? (
            <Card className="p-4 bg-muted border-0 rounded-lg">
              <div className="flex gap-4">
                <div className="w-16 h-16 bg-muted-foreground rounded-lg" />
                <div className="flex-1">
                  <div className="font-semibold text-foreground mb-1">Join the Project Underground Discord Server!</div>
                  <div className="text-sm text-muted-foreground">
                    Come compete, collab and learn from hundreds to thousands of other like minded...
                  </div>
                </div>
              </div>
            </Card>
          ) : type === "playlist" ? (
            <Card className="p-4 bg-muted border-0 rounded-lg">
              <div className="space-y-4">
                {/* Album Header */}
                <div className="flex items-center gap-3">
                  <Avatar className="w-8 h-8">
                    <AvatarFallback className="bg-muted-foreground text-primary-foreground text-xs font-medium">
                      RS
                    </AvatarFallback>
                  </Avatar>
                  <span className="text-sm font-medium text-foreground">RioStrap</span>
                </div>

                {/* Tags */}
                <div className="flex gap-2 text-sm text-muted-foreground">
                  <span>@bandlab</span>
                  <span>#hiphop</span>
                  <span>#trending</span>
                </div>

                {/* Album Info */}
                <div className="flex items-center gap-4">
                  <div className="w-16 h-16 bg-muted-foreground rounded-lg flex items-center justify-center">
                    <PlayCircle className="w-8 h-8 text-primary-foreground" />
                  </div>
                  <div className="flex-1">
                    <div className="font-semibold text-foreground">Hood Hop - Reloaded</div>
                    <div className="text-sm text-muted-foreground">Album by RioStrap • Feb 10, 2024</div>
                    <div className="text-sm text-muted-foreground">Hip Hop</div>
                  </div>
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <Heart className="w-4 h-4" />
                      <span>462</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <MessageCircle className="w-4 h-4" />
                      <span>78</span>
                    </div>
                  </div>
                </div>

                {/* Track List */}
                <div className="space-y-2">
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-foreground">1 &nbsp;&nbsp; Snakes (Ft. JayFlo)</span>
                    <span className="text-muted-foreground">2:18</span>
                  </div>
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-foreground">2 &nbsp;&nbsp; Smoke Alarm (Ft. RonToTheMFG)</span>
                    <span className="text-muted-foreground">2:12</span>
                  </div>
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-foreground">3 &nbsp;&nbsp; Wrist Broke (Ft. ybL J2l + UpstateMello)</span>
                    <span className="text-muted-foreground">3:03</span>
                  </div>
                  <div className="text-sm text-blue-600 cursor-pointer hover:text-blue-700">Show 11 more tracks</div>
                </div>
              </div>
            </Card>
          ) : (
            children
          )}
        </div>
      )}

      {/* Footer Stats */}
      <div className="flex items-center gap-6 text-muted-foreground">
        <Button
          variant="ghost"
          size="sm"
          className="flex items-center gap-2 text-muted-foreground hover:text-red-500 p-0 h-auto"
        >
          <Heart className="w-5 h-5" />
          <span className="text-sm">{stats.likes}</span>
        </Button>
        <Button
          variant="ghost"
          size="sm"
          className="flex items-center gap-2 text-muted-foreground hover:text-blue-500 p-0 h-auto"
        >
          <MessageCircle className="w-5 h-5" />
          <span className="text-sm">{stats.comments}</span>
        </Button>
        <Button variant="ghost" size="sm" className="text-muted-foreground hover:text-blue-500 p-0 h-auto">
          <Share className="w-5 h-5" />
        </Button>
      </div>
    </Card>
  )
}
