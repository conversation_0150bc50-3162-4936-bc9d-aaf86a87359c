/* eslint-disable react/no-unescaped-entities */
import React from "react";
import { PostInputCard, TrendingTracksCard, FeedCard } from ".";
import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import { PlayCircle} from "lucide-react";
import { ArtistListPage } from "@/features/artist/components";

// Mock data for trending tracks
const trendingTracks = [
  { title: "Track 1" },
  { title: "Track 2" },
  { title: "Track 3" },
  { title: "Track 4" },
  { title: "Track 5" },
];

// Mock data for feed
const feed = [
  // Music post
  {
    type: "music",
    userName: "<PERSON>",
    userRole: "Producer, Guitarist",
    time: "20 min",
    postContent:
      "Just wrapped up recording our new album! 🎵 Can't wait to share these tracks with you all. The sound has evolved so much since our last release. Special thanks to everyone who supported us during this journey. New single dropping next Friday! #NewMusic #StudioLife",
    stats: { likes: 148, comments: 58 },
    children: (
      <Card className="p-4 flex items-center gap-4 bg-muted rounded-l">
        <PlayCircle className="w-8 h-8" />
        <div className="flex-1">
          <div className="font-semibold">
            Cassanova{" "}
            <Badge variant="secondary" className="ml-2">
              Dance & EDM
            </Badge>
          </div>
          <div className="text-xs text-muted-foreground">Neal Altmayer • 2:30</div>
          <div className="h-2 w-full mt-2 rounded bg-muted-foreground/20" />
        </div>
      </Card>
    ),
  },
  // Opportunity post
  {
    type: "opportunity",
    userName: "Sonic Studios",
    userRole: "Producer, Guitarist",
    time: "20 min",
    postContent: (
      <>
        <div className="font-semibold">
          Music Producer Needed{" "}
          <Badge variant="secondary" className="ml-2">
            Opportunity
          </Badge>
        </div>
        <div className="text-xs text-muted-foreground">Project: Musically</div>
        <div>
          We're looking for a talented music producer to join our team. Experience with electronic music production and
          modern DAWs required. This is a remote position with flexible hours.
          <br />
          #MusicProduction #StudioLife #Electronic #Remote #DAW
        </div>
      </>
    ),
    badges: (
      <div className="flex gap-4 mt-2 text-muted-foreground text-xs">
        <Badge variant="outline">Remote</Badge>
        <span>less than a month</span>
        <span>$1200</span>
      </div>
    ),
    stats: { likes: 148, comments: 58 },
  },
  // Link preview post
  {
    type: "link",
    userName: "Mike Parker",
    userRole: "Song Lyricist",
    time: "42 min",
    postContent: (
      <>
        Ready to battle it out in the ultimate song competition? Don't hesitate—join the underground scene and
        collaborate with top-notch verified artists! Expand your fanbase and watch your music career soar!
        <br />
        <a href="https://discord.gg/6KkjaSDpCT" className="text-primary underline">
          https://discord.gg/6KkjaSDpCT
        </a>
      </>
    ),
    children: (
      <Card className="p-4 bg-muted rounded-l">
        <div className="font-semibold">Join the Project Underground Discord Server!</div>
        <div className="text-xs text-muted-foreground">
          Come compete, collab and learn from hundreds to thousands of other like minded...
        </div>
      </Card>
    ),
    stats: { likes: 148, comments: 58 },
  },
  // Playlist post
  {
    type: "playlist",
    userName: "RioStrap",
    userRole: "Composer, Bassist",
    time: "15 min",
    postContent: (
      <>
        Exciting News! I'm curating a playlist featuring 60 amazing artist songs for June! 🎶 Drop your favorite song
        title in the comments to get a chance to be included! ❤️ I'm here to spread the love to everyone who engages with
        this post!
        <br />
        <a
          href="https://www.smash.music.com/mirostrap/albums/10a5712d-a8c7-ee11-85f9-00224844f425"
          className="text-primary underline"
        >
          https://www.smash.music.com/mirostrap/albums/10a5712d-a8c7-ee11-85f9-00224844f425
        </a>
      </>
    ),
    children: (
      <Card className="p-4 bg-muted rounded-l">
        <div className="font-semibold">Hood Hop - Reloaded</div>
        <div className="text-xs text-muted-foreground">Album by RioStrap • Feb 10, 2024</div>
        <div className="text-xs text-muted-foreground">Hip Hop</div>
        <ol className="list-decimal pl-4 mt-2 text-xs">
          <li>
            Snaks (Ft. JayFlo) <span className="float-right">2:18</span>
          </li>
          <li>
            Smoke Alarm (Ft. RonToTheMFG) <span className="float-right">2:12</span>
          </li>
          <li>
            Wrist Broke (Ft. УВЫL J2l + UpstateMello) <span className="float-right">3:03</span>
          </li>
        </ol>
        <div className="text-xs text-primary mt-1 cursor-pointer">Show 11 more tracks</div>
        <div className="flex gap-4 mt-2 text-muted-foreground text-xs">
          <span>462</span>
          <span>78</span>
        </div>
      </Card>
    ),
    stats: { likes: 120, comments: 45 },
  },
]

const tabs = ["Latest", "Popular", "Following"];

interface DiscoverMainContentProps {
  active: string;
}

export default function DiscoverMainContent({active}: DiscoverMainContentProps) {
  return (
    <div className="flex-1 h-full overflow-hidden">
      {active==="Feed" && (
        <div className="h-full flex flex-col p-2 overflow-hidden">
          <div className="flex-shrink-0 space-y-6">
            <PostInputCard tabs={tabs} />
            <TrendingTracksCard tracks={trendingTracks} />
          </div>
          <div className="flex-1 min-h-0 overflow-y-auto mt-6">
            <div className="flex flex-col gap-6">
              {feed.map((item, idx) => (
                <FeedCard
                key={idx}
                userName={item.userName}
                userRole={item.userRole}
                time={item.time}
                  postContent={item.postContent}
                  badges={item.badges}
                  stats={item.stats}
                  type={item.type}
                  >
                  {item.children}
                </FeedCard>
              ))}
            </div>
          </div>
        </div>
      )}
      {active==="Artists" && <ArtistListPage/>}
    </div>
  );
}