"use client"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  Play,
  Heart,
  Plus,
  Edit3,
  Share2,
  Clock,
  Calendar,
  Music,
  MoreHorizontal,
  GripVertical,
  Trash2,
  Users,
  UserPlus,
} from "lucide-react"
import { useRouter } from "next/navigation"

const playlistSongs = [
  {
    id: 1,
    title: "Electric Skin",
    artist: "<PERSON> Rivers",
    album: "Neon Nightfall",
    duration: "3:52",
    cover: "/dummy-image.png?height=40&width=40",
  },
  {
    id: 2,
    title: "Neon Roads",
    artist: "Max Atlas",
    album: "Future Drive",
    duration: "4:15",
    cover: "/dummy-image.png?height=40&width=40",
  },
  {
    id: 3,
    title: "Binary Love",
    artist: "<PERSON>ira Blue",
    album: "Digital Dreams",
    duration: "3:45",
    cover: "/dummy-image.png?height=40&width=40",
  },
  {
    id: 4,
    title: "Sunset Boulevard",
    artist: "<PERSON> Smith",
    album: "Golden Hour",
    duration: "4:03",
    cover: "/dummy-image.png?height=40&width=40",
  },
  {
    id: 5,
    title: "Digital Waves",
    artist: "Neon Collective",
    album: "Synthwave Dreams",
    duration: "3:58",
    cover: "/dummy-image.png?height=40&width=40",
  },
]

export default function PlaylistPage() {
     const router = useRouter();
     
      const handleSongClick = () => {
        router.push(`/song`);
      };
      const handleArtistClick = () => {
        router.push(`/artist`);
      };
  return (
    <div className="min-h-screen bg-background">
      <div className="px-4 md:px-8 lg:px-16 py-8">
        {/* Header / Hero Section */}
        <div className="flex flex-col lg:flex-row gap-8 mb-8">
          {/* Playlist Cover */}
          <div className="flex-shrink-0">
            <Image
              src="/dummy-image.png?height=300&width=300"
              alt="Playlist Cover"
              width={300}
              height={300}
              className="rounded-lg shadow-lg"
            />
          </div>

          {/* Playlist Info */}
          <div className="flex-1 space-y-6">
            <div className="space-y-4">
              <div className="space-y-2">
                <Badge variant="outline" className="w-fit">
                  Playlist
                </Badge>
                <h1 className="text-4xl md:text-5xl font-bold">Synthwave Sundays</h1>
              </div>

              <p className="text-lg text-muted-foreground max-w-2xl">
                A curated mix of modern synthwave tracks to relax, code, or cruise to.
              </p>

              <div className="flex flex-wrap items-center gap-4 text-muted-foreground">
                <div className="flex items-center gap-2">
                  <Avatar className="w-6 h-6">
                    <AvatarImage src="/placeholder.svg?height=24&width=24" />
                    <AvatarFallback>A</AvatarFallback>
                  </Avatar>
                  <span className="text-primary cursor-pointer hover:underline">@alex</span>
                </div>
                <div className="flex items-center gap-1">
                  <Calendar className="w-4 h-4" />
                  <span>Created January 2024</span>
                </div>
                <div className="flex items-center gap-1">
                  <Music className="w-4 h-4" />
                  <span>{playlistSongs.length} songs</span>
                </div>
                <div className="flex items-center gap-1">
                  <Clock className="w-4 h-4" />
                  <span>11 min 52 sec</span>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-wrap gap-3">
              <Button size="lg" className="gap-2" disabled={true}>
                <Play className="w-5 h-5" />
                Play All
              </Button>
              <Button variant="outline" size="lg" className="gap-2">
                <Heart className="w-5 h-5" />
                Like (3.5K)
              </Button>
              <Button variant="outline" size="lg" className="gap-2">
                <Plus className="w-5 h-5" />
                Add Song
              </Button>
              <Button variant="outline" size="lg" className="gap-2">
                <UserPlus className="w-5 h-5" />
                Follow
              </Button>
              <Button variant="outline" size="lg" className="gap-2">
                <Edit3 className="w-5 h-5" />
                Edit
              </Button>
              <Button variant="outline" size="lg" className="gap-2">
                <Share2 className="w-5 h-5" />
                Share
              </Button>
            </div>
          </div>
        </div>

        {/* Songs Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Songs in Playlist</span>
              <Button variant="outline" size="sm">
                <Plus className="w-4 h-4 mr-2" />
                Add New Song
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {/* Table Header */}
              <div className="grid grid-cols-12 gap-4 px-4 py-2 text-sm font-medium text-muted-foreground border-b">
                <div className="col-span-1">#</div>
                <div className="col-span-5">Title</div>
                <div className="col-span-3">Album</div>
                <div className="col-span-2">Duration</div>
                <div className="col-span-1"></div>
              </div>

              {/* Song Rows */}
              {playlistSongs.map((song, index) => (
                <div
                  key={song.id}
                  className="grid grid-cols-12 gap-4 px-4 py-3 rounded-lg hover:bg-muted/50 group cursor-pointer transition-colors"
                  onClick={() => handleSongClick()}
                >
                  <div className="col-span-1 flex items-center">
                    <GripVertical className="w-4 h-4 text-muted-foreground opacity-0 group-hover:opacity-100 mr-2" />
                    <span className="text-muted-foreground">{index + 1}</span>
                    <Play className="w-4 h-4 hidden opacity-50 cursor-not-allowed" />
                  </div>

                  <div className="col-span-5 flex items-center gap-3">
                    <Image
                      src={song.cover || "/dummy-image.png"}
                      alt="Song Cover"
                      width={40}
                      height={40}
                      className="rounded"
                    />
                    <div>
                      <p className="font-medium">{song.title}</p>
                      <p
                        onClick={(e) => {
                          e.stopPropagation();
                          handleArtistClick();
                        }}
                        className="text-sm text-muted-foreground text-primary cursor-pointer hover:underline"
                      >
                        {song.artist}
                      </p>
                    </div>
                  </div>

                  <div className="col-span-3 flex items-center">
                    <span
                      onClick={(e) => {
                        e.stopPropagation();
                        // Add album click handler here if needed
                      }}
                      className="text-sm text-muted-foreground text-primary cursor-pointer hover:underline"
                    >
                      {song.album}
                    </span>
                  </div>

                  <div className="col-span-2 flex items-center">
                    <span className="text-sm text-muted-foreground">{song.duration}</span>
                  </div>

                  <div className="col-span-1 flex items-center justify-end">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="opacity-0 group-hover:opacity-100"
                      onClick={(e) => {
                        e.stopPropagation(); // Prevent row click when clicking the button
                      }}
                    >
                      <MoreHorizontal className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Playlist Stats & Info */}
        <div className="grid md:grid-cols-2 gap-6 mt-8">
          <Card>
            <CardHeader>
              <CardTitle>Playlist Statistics</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Total Likes</span>
                <span className="font-medium">247</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Followers</span>
                <span className="font-medium">1,892</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Total Plays</span>
                <span className="font-medium">15,634</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Last Updated</span>
                <span className="font-medium">2 days ago</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Playlist Info</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <p className="text-sm font-medium">Created by</p>
                <div className="flex items-center gap-2">
                  <Avatar className="w-8 h-8">
                    <AvatarImage src="/placeholder.svg?height=32&width=32" />
                    <AvatarFallback>AM</AvatarFallback>
                  </Avatar>
                  <span className="text-primary cursor-pointer hover:underline">Alex Manager</span>
                </div>
              </div>

              <div className="space-y-2">
                <p className="text-sm font-medium">Visibility</p>
                <Badge variant="secondary">Public</Badge>
              </div>

              <div className="space-y-2">
                <p className="text-sm font-medium">Collaborative</p>
                <span className="text-sm text-muted-foreground">No</span>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Manager Tools */}
        <Card className="mt-8 border-dashed border-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-muted-foreground">
              <Edit3 className="w-5 h-5" />
              Playlist Management
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-4">
              <Button variant="outline" className="justify-start">
                <Edit3 className="w-4 h-4 mr-2" />
                Edit Details
              </Button>
              <Button variant="outline" className="justify-start">
                <Plus className="w-4 h-4 mr-2" />
                Add Songs
              </Button>
              <Button variant="outline" className="justify-start">
                <Users className="w-4 h-4 mr-2" />
                Manage Access
              </Button>
              <Button variant="outline" className="justify-start">
                <Share2 className="w-4 h-4 mr-2" />
                Share Settings
              </Button>
              <Button variant="outline" className="justify-start">
                <Music className="w-4 h-4 mr-2" />
                Reorder Songs
              </Button>
              <Button variant="outline" className="justify-start text-destructive">
                <Trash2 className="w-4 h-4 mr-2" />
                Delete Playlist
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
