export interface Opportunity {
  id: string
  title: string
  role: string
  description: string
  postedBy: {
    id: string
    name: string
    type: "artist" | "person" | "band"
    avatar?: string
  }
  genre: string[]
  timeline: {
    startDate: string
    endDate: string
  }
  location: {
    type: "remote" | "in-person" | "hybrid"
    city?: string
    country?: string
  }
  payment: {
    type: "paid" | "unpaid" | "negotiable"
    amount?: number
    currency?: string
  }
  associatedProject?: {
    id: string
    name: string
    type: "album" | "song" | "artist"
  }
  requirements: string[]
  status: "open" | "closed" | "filled"
  applicationsCount: number
  createdAt: string
  updatedAt: string
}

export interface Application {
  id: string
  opportunityId: string
  opportunity: Opportunity
  applicantId: string
  applicant: {
    id: string
    name: string
    avatar?: string
  }
  status: "pending" | "accepted" | "rejected"
  message: string
  attachments?: string[]
  appliedAt: string
}

export const ROLES = [
  "Vocalist",
  "Guitarist",
  "Bassist",
  "Drummer",
  "Keyboardist",
  "Producer",
  "Sound Engineer",
  "Mixing Engineer",
  "Mastering Engineer",
  "Songwriter",
  "Composer",
  "Session Musician",
  "Backup Vocalist",
  "Music Director",
  "Other",
] as const

export const GENRES = [
  "Rock",
  "Pop",
  "Jazz",
  "Classical",
  "Electronic",
  "Hip Hop",
  "R&B",
  "Country",
  "Folk",
  "Blues",
  "Reggae",
  "Punk",
  "Metal",
  "Indie",
  "Alternative",
  "World Music",
] as const
