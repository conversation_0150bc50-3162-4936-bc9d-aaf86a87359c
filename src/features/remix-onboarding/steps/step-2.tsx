'use client';

import React, { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { useRemixOnboarding, validateStep2 } from '../remix-onboarding-context';
import Image from 'next/image';

export default function Step2() {
  const { data, updateData, nextStep, prevStep } = useRemixOnboarding();
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (file: File) => {
    if (file && file.type.startsWith('image/')) {
      const url = URL.createObjectURL(file);
      updateData({
        profilePhoto: file,
        profilePhotoUrl: url,
      });
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  const handleContinue = () => {
    if (validateStep2(data)) {
      nextStep();
    }
  };

  const isValid = data.profilePhotoUrl.length > 0;

  const handleBack = () => {
    prevStep();
  };

  return (
    <div className="space-y-6">
      {/* Title */}
      <h1 className= 'text-foreground text-3xl font-bold font-arvo leading-10'>
          Choose a profile photo
        </h1>

      {/* Upload Area */}
      <div className="space-y-5">
        {data.profilePhotoUrl ? (
          /* Photo Uploaded State */
          <div className="px-6 py-8 bg-white rounded-xl shadow-[0px_4px_20px_0px_rgba(0,0,0,0.08)] outline outline-1 outline-offset-[-0.50px] outline-[#f0f0f0] flex flex-col justify-start items-center gap-6">
       {data.profilePhotoUrl &&  <h1 className={'text-foreground text-lg font-semibold font-lato leading-normal'}>
          Looking Good !
        </h1>}
            <div className="w-[140px] h-[140px] rounded-full overflow-hidden">
              <Image
                src={data.profilePhotoUrl}
                alt="Profile preview"
                width={140}
                height={140}
                className="w-full h-full object-cover"
              />
            </div>
            <Button
              onClick={handleUploadClick}
              className="bg-black text-white text-sm font-bold font-arvo leading-[21px] px-8 py-6 rounded-full hover:bg-gray-800"
            >
              Change Photo
            </Button>
          </div>
        ) : (
          /* Initial Upload State */
          <>
            {/* Drag & Drop Area */}
            <div
              onDrop={handleDrop}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              className={`border-2 border-dashed rounded-xl p-14 text-center transition-colors ${
                isDragOver
                  ? 'border-primary bg-primary/5'
                  : 'border-muted-foreground bg-transparent'
              }`}
            >
              <div className="space-y-2">
                <h3 className="text-lg font-bold text-card-foreground font-arvo">
                  Add a profile photo
                </h3>
                <p className="text-sm text-muted-foreground font-lato">
                  Drag and drop or click to upload
                </p>
              <Button
                onClick={handleUploadClick}
                className="bg-primary text-primary-foreground font-arvo font-bold text-sm px-4 py-2.5 rounded-full hover:bg-primary/90 mt-4"
              >
                Upload a photo
              </Button>
            
              </div>
              
            </div>

            {/* Upload Button */}
          
          </>
        )}

        {/* Hidden File Input */}
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleFileInputChange}
          className="hidden"
        />
      </div>

      {/* Navigation Buttons */}
      <div className="flex justify-between items-center pt-6">
        <Button
          onClick={handleBack}
          variant="outline"
          className="px-6 py-3 font-arvo font-bold text-base border-muted-foreground text-muted-foreground hover:bg-muted"
        >
          Back
        </Button>
        <Button
          onClick={handleContinue}
          disabled={!isValid}
          className="px-6 py-3 bg-primary text-primary-foreground font-arvo font-bold text-base hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Continue
        </Button>
      </div>
    </div>
  );
}
