'use client';

import { useRouter } from 'next/navigation';
import React, { createContext, useContext, useState, ReactNode } from 'react';

// Types for form data
export interface RemixOnboardingData {
  // Step 1: Personal Info
  realName: string;
  artistName: string;
  
  // Step 2: Profile Photo
  profilePhoto: File | null;
  profilePhotoUrl: string;
  
  // Step 3: Bio, Location and Phone
  bio: string;
  location: string;
  phoneNumber: string;
  
  // Step 4: Role Selection
  roles: {
    songwriter: boolean;
    toplineWriter: boolean;
    lyricist: boolean;
    melodywriter: boolean;
    beatmaker: boolean;
    arranger: boolean;
    remixer: boolean;
    vocalist: boolean;
    musician: boolean;
    engineer: boolean;
    artist: boolean;
  };
  
  // Step 5: Social Links
  socialLinks: {
    spotify: string;
    appleMusic: string;
    youtube: string;
    instagram: string;
    linkedin: string;
    website: string;
  };
}

interface RemixOnboardingContextType {
  step: number;
  data: RemixOnboardingData;
  isLoading: boolean;
  nextStep: () => void;
  prevStep: () => void;
  goToStep: (step: number) => void;
  updateData: (updates: Partial<RemixOnboardingData>) => void;
  submitOnboarding: () => Promise<void>;
  resetOnboarding: () => void;
}

const initialData: RemixOnboardingData = {
  realName: '',
  artistName: '',
  profilePhoto: null,
  profilePhotoUrl: '',
  bio: '',
  location: '',
  phoneNumber: '',
  roles: {
    songwriter: false,
    toplineWriter: false,
    lyricist: false,
    melodywriter: false,
    beatmaker: false,
    arranger: false,
    remixer: false,
    vocalist: false,
    musician: false,
    engineer: false,
    artist: false,
  },
  socialLinks: {
    spotify: '',
    appleMusic: '',
    youtube: '',
    instagram: '',
    linkedin: '',
    website: '',
  },
};

const RemixOnboardingContext = createContext<RemixOnboardingContextType | undefined>(undefined);

interface RemixOnboardingProviderProps {
  children: ReactNode;
}

export function RemixOnboardingProvider({ children }: RemixOnboardingProviderProps) {
  const [step, setStep] = useState(1);
  const [data, setData] = useState<RemixOnboardingData>(initialData);
  const [isLoading, setIsLoading] = useState(false);

  const nextStep = () => {
    if (step < 5) {
      setStep(step + 1);
    }
  };

  const prevStep = () => {
    if (step > 1) {
      setStep(step - 1);
    }
  };

  const goToStep = (targetStep: number) => {
    if (targetStep >= 1 && targetStep <= 5) {
      setStep(targetStep);
    }
  };

  const updateData = (updates: Partial<RemixOnboardingData>) => {
    setData(prev => ({
      ...prev,
      ...updates,
      // Handle nested objects properly
      ...(updates.roles && {
        roles: { ...prev.roles, ...updates.roles }
      }),
      ...(updates.socialLinks && {
        socialLinks: { ...prev.socialLinks, ...updates.socialLinks }
      }),
    }));
  };
const router = useRouter()
  const submitOnboarding = async () => {
    setIsLoading(true);
    try {
      // TODO: Implement API call to submit onboarding data
      console.log('Submitting remix onboarding data:', data);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Mark remix onboarding as complete
      localStorage.setItem('remix-onboarding', 'done');

      // Check if there's a pending action (like opening upload modal)
      const pendingAction = localStorage.getItem('remix-pending-action');

      if (pendingAction === 'upload-modal') {
        // Redirect to remix page with onboarding=done to trigger upload modal
        router.push('/remix?onboarding=done');
      } else {
        // No pending action, just redirect to remix page
        router.push('/remix');
      }
    } catch (error) {
      console.error('Error submitting remix onboarding:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const resetOnboarding = () => {
    setStep(1);
    setData(initialData);
    setIsLoading(false);
    localStorage.removeItem('remix-onboarding');
  };

  const value: RemixOnboardingContextType = {
    step,
    data,
    isLoading,
    nextStep,
    prevStep,
    goToStep,
    updateData,
    submitOnboarding,
    resetOnboarding,
  };

  return (
    <RemixOnboardingContext.Provider value={value}>
      {children}
    </RemixOnboardingContext.Provider>
  );
}

export function useRemixOnboarding() {
  const context = useContext(RemixOnboardingContext);
  if (context === undefined) {
    throw new Error('useRemixOnboarding must be used within a RemixOnboardingProvider');
  }
  return context;
}

// Validation helpers
export const validateStep1 = (data: RemixOnboardingData): boolean => {
  return data.realName.trim().length > 0 && data.artistName.trim().length > 0;
};

export const validateStep2 = (data: RemixOnboardingData): boolean => {
  console.log("Validating step 2==", data);

  // Profile photo is now required
  return data.profilePhotoUrl.length > 0;
};

export const validateStep3 = (data: RemixOnboardingData): boolean => {
  const isValidPhoneNumber = (phone: string): boolean => {
    // Remove all non-digit characters
    const cleanPhone = phone.replace(/\D/g, '');
    // Check if it's a valid length (10-15 digits for international numbers)
    return cleanPhone.length >= 10 && cleanPhone.length <= 15;
  };

  return (
    data.bio.trim().length > 0 &&
    data.location.trim().length > 0 &&
    data.phoneNumber.trim().length > 0 &&
    isValidPhoneNumber(data.phoneNumber)
  );
};

export const validateStep4 = (data: RemixOnboardingData): boolean => {
  // At least one role should be selected
  return Object.values(data.roles).some(role => role);
};

export const validateStep5 = (data: RemixOnboardingData): boolean => {
  console.log("Validating step 5==", data);
  // Social links are optional, so always return true
  return true;
};

export const validateCurrentStep = (step: number, data: RemixOnboardingData): boolean => {
  switch (step) {
    case 1:
      return validateStep1(data);
    case 2:
      return validateStep2(data);
    case 3:
      return validateStep3(data);
    case 4:
      return validateStep4(data);
    case 5:
      return validateStep5(data);
    default:
      return false;
  }
};
