'use client';

import React from 'react';
import { useRemixOnboarding } from './remix-onboarding-context';

export function RemixOnboardingStepper() {
  const { step } = useRemixOnboarding();

  return (
    <div className="space-y-3">
      {/* Step Label */}
      <div className="text-sm text-muted-foreground font-inter">
        Step {step}/5
      </div>
      
      {/* Progress Bar */}
      <div className="flex items-center gap-1.5">
        {[1, 2, 3, 4, 5].map((stepNumber) => (
          <div
            key={stepNumber}
            className={`h-0.5 flex-1 rounded-full transition-colors duration-200 ${
              stepNumber <= step
                ? 'bg-primary'
                : 'bg-muted'
            }`}
          />
        ))}
      </div>
    </div>
  );
}
