/**
 * File Upload Utilities for Remix Contest
 * Handles file validation, upload progress, and file management
 */

export interface UploadFile {
  id: string;
  file: File;
  name: string;
  size: number;
  type: string;
  status: 'pending' | 'uploading' | 'completed' | 'error';
  progress: number;
  url?: string;
  error?: string;
}

export interface FileValidationResult {
  isValid: boolean;
  error?: string;
}

// Supported file types for remix contest
export const SUPPORTED_FILE_TYPES = ['audio/wav', 'audio/mpeg', 'audio/mp3', 'audio/aiff', 'audio/x-aiff', 'audio/mp4', 'audio/aac'];
export const SUPPORTED_EXTENSIONS = ['.aif', '.aiff', '.wav', '.mp3', '.mp4', '.aac'];
export const MAX_FILE_SIZE = 100 * 1024 * 1024; // 100MB
export const MAX_FILES = 10;

/**
 * Validate a single file for remix upload
 */
export function validateFile(file: File): FileValidationResult {
  // Check file type
  const isValidType = SUPPORTED_FILE_TYPES.includes(file.type) || 
    SUPPORTED_EXTENSIONS.some(ext => file.name.toLowerCase().endsWith(ext));
  
  if (!isValidType) {
    const supportedExtensions = SUPPORTED_EXTENSIONS.join(', ');
    return {
      isValid: false,
      error: `We only accept audio files with file extension ${supportedExtensions}`
    };
  }

  // Check file size
  if (file.size > MAX_FILE_SIZE) {
    return {
      isValid: false,
      error: `File size must be less than ${Math.round(MAX_FILE_SIZE / (1024 * 1024))}MB`
    };
  }

  // Check if file name is valid
  if (!file.name || file.name.trim().length === 0) {
    return {
      isValid: false,
      error: 'File must have a valid name'
    };
  }

  return { isValid: true };
}

/**
 * Validate multiple files
 */
export function validateFiles(files: File[], existingFiles: UploadFile[] = []): {
  validFiles: File[];
  errors: string[];
} {
  const validFiles: File[] = [];
  const errors: string[] = [];

  // Check total file count
  if (files.length + existingFiles.length > MAX_FILES) {
    errors.push(`Maximum ${MAX_FILES} files allowed`);
    return { validFiles, errors };
  }

  // Check for duplicate names
  const existingNames = existingFiles.map(f => f.name.toLowerCase());
  const newNames = new Set<string>();

  for (const file of files) {
    const fileName = file.name.toLowerCase();
    
    // Check for duplicates with existing files
    if (existingNames.includes(fileName)) {
      errors.push(`File "${file.name}" already exists`);
      continue;
    }

    // Check for duplicates within new files
    if (newNames.has(fileName)) {
      errors.push(`Duplicate file name: "${file.name}"`);
      continue;
    }

    // Validate individual file
    const validation = validateFile(file);
    if (!validation.isValid) {
      errors.push(`${file.name}: ${validation.error}`);
      continue;
    }

    validFiles.push(file);
    newNames.add(fileName);
  }

  return { validFiles, errors };
}

/**
 * Create UploadFile objects from File objects
 */
export function createUploadFiles(files: File[]): UploadFile[] {
  return files.map(file => ({
    id: generateFileId(),
    file,
    name: file.name,
    size: file.size,
    type: file.type,
    status: 'pending',
    progress: 0
  }));
}

/**
 * Generate unique file ID
 */
function generateFileId(): string {
  return `file_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Format file size for display
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Get file extension from filename
 */
export function getFileExtension(filename: string): string {
  return filename.split('.').pop()?.toLowerCase() || '';
}

/**
 * Check if file is audio type
 */
export function isAudioFile(file: File | UploadFile): boolean {
  const type = 'file' in file ? file.file.type : file.type;
  const name = 'file' in file ? file.file.name : file.name;
  
  return SUPPORTED_FILE_TYPES.includes(type) || 
    SUPPORTED_EXTENSIONS.some(ext => name.toLowerCase().endsWith(ext));
}

/**
 * Simulate file upload with progress
 * In a real app, this would make actual HTTP requests
 */
export async function uploadFile(
  uploadFile: UploadFile,
  onProgress: (progress: number) => void
): Promise<{ success: boolean; url?: string; error?: string }> {
  return new Promise((resolve) => {
    let progress = 0;
    const interval = setInterval(() => {
      progress += Math.random() * 15;
      if (progress > 100) progress = 100;
      
      onProgress(progress);
      
      if (progress >= 100) {
        clearInterval(interval);
        
        // Simulate occasional upload failures
        const success = Math.random() > 0.1; // 90% success rate
        
        if (success) {
          // Create a blob URL for the uploaded file for demo purposes
          const url = URL.createObjectURL(uploadFile.file);
          resolve({ success: true, url });
        } else {
          resolve({ 
            success: false, 
            error: 'Upload failed. Please try again.' 
          });
        }
      }
    }, 100);
  });
}

/**
 * Download file from URL
 */
export function downloadFile(url: string, filename: string): void {
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

/**
 * Create audio URL for playback
 */
export function createAudioUrl(file: File): string {
  return URL.createObjectURL(file);
}

/**
 * Cleanup audio URL
 */
export function cleanupAudioUrl(url: string): void {
  URL.revokeObjectURL(url);
}

/**
 * Format duration in MM:SS format
 */
export function formatDuration(seconds: number): string {
  if (isNaN(seconds) || seconds < 0) return '0:00';

  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);

  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
}
