"use client"

import { useAuth } from '@/contexts/auth/auth-context'
import { useRouter } from 'next/navigation'
import { useCallback } from 'react'

export function useCognitoAuth() {
  const { signOut: authSignOut, isLoading } = useAuth()
  const router = useRouter()

  const signOut = useCallback(async () => {
    await authSignOut()
    router.push('/login')
  }, [authSignOut, router])

  // For backward compatibility - redirect to signup page
  const signUp = useCallback(() => {
    router.push('/signup')
  }, [router])

  // For backward compatibility - redirect to login page
  const signIn = useCallback(() => {
    router.push('/login')
  }, [router])

  return {
    signIn,
    signUp,
    signOut,
    isLoading,
  }
}
