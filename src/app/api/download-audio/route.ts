import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const audioUrl = searchParams.get('url');
    
    if (!audioUrl) {
      return NextResponse.json({ error: 'URL parameter is required' }, { status: 400 });
    }
    
    // Validate that it's the expected audio file URL
    const allowedDomains = ['d1jds31zoh6k0w.cloudfront.net'];
    const urlObj = new URL(audioUrl);
    
    if (!allowedDomains.includes(urlObj.hostname)) {
      return NextResponse.json({ error: 'Invalid URL domain' }, { status: 403 });
    }
    
    // Fetch the audio file
    const response = await fetch(audioUrl, {
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; SMASH Music Download)',
      },
    });
    
    if (!response.ok) {
      return NextResponse.json({ error: 'Failed to fetch audio file' }, { status: response.status });
    }
    
    // Get the audio data
    const audioBuffer = await response.arrayBuffer();
    
    // Return the audio file with proper headers for download
    return new NextResponse(audioBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/octet-stream',
        'Content-Disposition': 'attachment; filename="ATTENTION_Open_Verse.wav"',
        'Content-Length': audioBuffer.byteLength.toString(),
        'Cache-Control': 'no-cache',
      },
    });
    
  } catch (error) {
    console.error('Download proxy error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
