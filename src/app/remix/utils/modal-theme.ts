'use client';

import { useTheme } from '@/contexts/theme/theme-context';
import { remixThemeConfig } from '../theme-config';

/**
 * Utility hook to get remix theme CSS variables for modals
 * Use this in any modal component within the remix section
 * 
 * @example
 * ```tsx
 * import { useRemixModalTheme } from '@/app/remix/utils/modal-theme';
 * 
 * export function MyModal({ isOpen, onClose }) {
 *   const { cssVariables, themeMode } = useRemixModalTheme();
 *   
 *   return (
 *     <Dialog open={isOpen} onOpenChange={onClose}>
 *       <DialogContent style={cssVariables} data-theme={themeMode}>
 *         <Button className="bg-primary text-primary-foreground">
 *           Themed Button
 *         </Button>
 *       </DialogContent>
 *     </Dialog>
 *   );
 * }
 * ```
 */
export function useRemixModalTheme() {
  const { resolvedTheme } = useTheme();
  
  const mode = resolvedTheme === 'dark' ? 'dark' : 'light';
  const themeColors = remixThemeConfig[mode];
  
  const cssVariables: React.CSSProperties = {
    '--primary': `hsl(${themeColors.primary.DEFAULT})`,
    '--primary-foreground': `hsl(${themeColors.primary.foreground})`,
    '--secondary': `hsl(${themeColors.secondary.DEFAULT})`,
    '--secondary-foreground': `hsl(${themeColors.secondary.foreground})`,
    '--muted': `hsl(${themeColors.muted.DEFAULT})`,
    '--muted-foreground': `hsl(${themeColors.muted.foreground})`,
    '--accent': `hsl(${themeColors.accent.DEFAULT})`,
    '--accent-foreground': `hsl(${themeColors.accent.foreground})`,
    '--background': `hsl(${themeColors.background})`,
    '--foreground': `hsl(${themeColors.foreground})`,
    '--card': `hsl(${themeColors.card.DEFAULT})`,
    '--card-foreground': `hsl(${themeColors.card.foreground})`,
    '--border': `hsl(${themeColors.border})`,
    '--input': `hsl(${themeColors.input})`,
    '--ring': `hsl(${themeColors.ring})`,
    '--destructive': `hsl(${themeColors.destructive.DEFAULT})`,
    '--destructive-foreground': `hsl(${themeColors.destructive.foreground})`,
  } as React.CSSProperties;
  
  return {
    cssVariables,
    themeMode: mode,
    themeColors
  };
}
