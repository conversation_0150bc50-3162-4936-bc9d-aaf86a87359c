'use client';

import React, { useEffect, useState } from 'react';
import { RemixOnboardingProvider } from '@/features/remix-onboarding/remix-onboarding-context';
import { RemixOnboardingStepper } from '@/features/remix-onboarding/remix-onboarding-stepper';
import dynamic from 'next/dynamic';
import { useRouter } from 'next/navigation';
import { Loader2 } from 'lucide-react';

const steps = [
  dynamic(() => import('@/features/remix-onboarding/steps/step-1')),
  dynamic(() => import('@/features/remix-onboarding/steps/step-2')),
  dynamic(() => import('@/features/remix-onboarding/steps/step-3')),
  dynamic(() => import('@/features/remix-onboarding/steps/step-4')),
  dynamic(() => import('@/features/remix-onboarding/steps/step-5')),
];

import { useRemixOnboarding } from '@/features/remix-onboarding/remix-onboarding-context';

function RemixOnboardingSteps() {
  const { step } = useRemixOnboarding();
  const StepComponent = steps[step - 1];

  return (
    <div className="min-h-screen bg-background flex flex-col items-center justify-start md:justify-center px-4 py-10">
      <div className="w-full max-w-md mx-auto">
        {/* Progress Stepper */}
        <div className="mb-8">
          <RemixOnboardingStepper />
        </div>
        
        {/* Step Content */}
        <div className="bg-background">
          <StepComponent />
        </div>
      </div>
    </div>
  );
}

export default function RemixOnboardingPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check remix onboarding status on client side only
    const checkOnboardingStatus = () => {
      try {
        const remixOnboardingStatus = localStorage.getItem("remix-onboarding");

        if (remixOnboardingStatus === "done") {
          router.push('/remix');
          return;
        }

        setIsLoading(false);
      } catch (error) {
        console.error("Error checking remix onboarding status:", error);
        setIsLoading(false);
      }
    };

    // Add a small delay to ensure the page is fully mounted
    const timer = setTimeout(checkOnboardingStatus, 100);
    return () => clearTimeout(timer);
  }, [router]);

  // Show loading while checking onboarding status
  if (isLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
          <h2 className="text-xl font-semibold text-foreground">Loading...</h2>
          <p className="text-muted-foreground">Checking your onboarding status</p>
        </div>
      </div>
    );
  }

  return (
    <RemixOnboardingProvider>
      <RemixOnboardingSteps />
    </RemixOnboardingProvider>
  );
}
