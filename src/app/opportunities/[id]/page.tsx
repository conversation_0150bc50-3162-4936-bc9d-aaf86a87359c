import OpportunityDetailClient from "./OpportunityDetailClient"

// Generate static params for static export
export async function generateStaticParams() {
  // Return a list of possible IDs for static generation
  // In a real app, you would fetch this from your API or database
  return [
    { id: '1' },
    { id: '2' },
    { id: '3' },
    { id: '4' },
    { id: '5' },
  ];
}

export default function OpportunityDetailPage() {
  return <OpportunityDetailClient />;
}
