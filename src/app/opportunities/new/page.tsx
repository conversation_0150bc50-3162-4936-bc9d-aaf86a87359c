"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft, Plus, X } from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { ROLES, GENRES } from "../../../features/opportunites/types"

const formSchema = z.object({
  title: z.string().min(5, "Title must be at least 5 characters"),
  role: z.string().min(1, "Please select a role"),
  description: z.string().min(20, "Description must be at least 20 characters"),
  genres: z.array(z.string()).min(1, "Please select at least one genre"),
  timeline: z.object({
    startDate: z.string().min(1, "Start date is required"),
    endDate: z.string().min(1, "End date is required"),
  }),
  location: z.object({
    type: z.enum(["remote", "in-person", "hybrid"]),
    city: z.string().optional(),
    country: z.string().optional(),
  }),
  payment: z.object({
    type: z.enum(["paid", "unpaid", "negotiable"]),
    amount: z.number().optional(),
    currency: z.string().optional(),
  }),
  requirements: z.array(z.string()),
  associatedProject: z.string().optional(),
})

type FormValues = z.infer<typeof formSchema>

export default function NewOpportunityPage() {
  const [selectedGenres, setSelectedGenres] = useState<string[]>([])
  const [requirements, setRequirements] = useState<string[]>([])
  const [newRequirement, setNewRequirement] = useState("")
  const router = useRouter()

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: "",
      role: "",
      description: "",
      genres: [],
      timeline: {
        startDate: "",
        endDate: "",
      },
      location: {
        type: "remote",
        city: "",
        country: "",
      },
      payment: {
        type: "negotiable",
        currency: "₹",
      },
      requirements: [],
      associatedProject: "",
    },
  })

  const addGenre = (genre: string) => {
    if (!selectedGenres.includes(genre)) {
      const newGenres = [...selectedGenres, genre]
      setSelectedGenres(newGenres)
      form.setValue("genres", newGenres)
    }
  }

  const removeGenre = (genre: string) => {
    const newGenres = selectedGenres.filter((g) => g !== genre)
    setSelectedGenres(newGenres)
    form.setValue("genres", newGenres)
  }

  const addRequirement = () => {
    if (newRequirement.trim() && !requirements.includes(newRequirement.trim())) {
      const newRequirements = [...requirements, newRequirement.trim()]
      setRequirements(newRequirements)
      form.setValue("requirements", newRequirements)
      setNewRequirement("")
    }
  }

  const removeRequirement = (requirement: string) => {
    const newRequirements = requirements.filter((r) => r !== requirement)
    setRequirements(newRequirements)
    form.setValue("requirements", newRequirements)
  }

  async function onSubmit(values: FormValues) {
    try {
      // Here you would submit to your API
      console.log("Submitting opportunity:", values)

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000))

      // Redirect to opportunities page
      router.push("/opportunities")
    } catch (error) {
      console.error("Error creating opportunity:", error)
    }
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="px-4 md:px-8 lg:px-16 py-8">
        {/* Header */}
        <div className="flex items-center gap-4 mb-8">
          <Link href="/opportunities">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Opportunities
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold">Post New Opportunity</h1>
            <p className="text-muted-foreground">Find the perfect collaborator for your project</p>
          </div>
        </div>

        <div className="max-w-2xl mx-auto">
          <Card>
            <CardHeader>
              <CardTitle>Opportunity Details</CardTitle>
            </CardHeader>
            <CardContent>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                  {/* Title */}
                  <FormField
                    control={form.control}
                    name="title"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Opportunity Title</FormLabel>
                        <FormControl>
                          <Input placeholder="e.g., Looking for Jazz Drummer" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Role */}
                  <FormField
                    control={form.control}
                    name="role"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Role Needed</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select a role" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {ROLES.map((role) => (
                              <SelectItem key={role} value={role}>
                                {role}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Description */}
                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Describe what you're looking for, the project details, and any specific requirements..."
                            className="min-h-[120px]"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Genres */}
                  <div className="space-y-3">
                    <FormLabel>Genres</FormLabel>
                    <div className="flex flex-wrap gap-2 mb-3">
                      {selectedGenres.map((genre) => (
                        <Badge key={genre} variant="secondary" className="gap-1">
                          {genre}
                          <X className="w-3 h-3 cursor-pointer" onClick={() => removeGenre(genre)} />
                        </Badge>
                      ))}
                    </div>
                    <Select onValueChange={addGenre}>
                      <SelectTrigger>
                        <SelectValue placeholder="Add genres" />
                      </SelectTrigger>
                      <SelectContent>
                        {GENRES.filter((genre) => !selectedGenres.includes(genre)).map((genre) => (
                          <SelectItem key={genre} value={genre}>
                            {genre}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {form.formState.errors.genres && (
                      <p className="text-sm text-destructive">{form.formState.errors.genres.message}</p>
                    )}
                  </div>

                  {/* Timeline */}
                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="timeline.startDate"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Start Date</FormLabel>
                          <FormControl>
                            <Input type="date" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="timeline.endDate"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>End Date</FormLabel>
                          <FormControl>
                            <Input type="date" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Location */}
                  <FormField
                    control={form.control}
                    name="location.type"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Location Type</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="remote">Remote</SelectItem>
                            <SelectItem value="in-person">In-Person</SelectItem>
                            <SelectItem value="hybrid">Hybrid</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Payment */}
                  <div className="space-y-4">
                    <FormField
                      control={form.control}
                      name="payment.type"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Payment Type</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="paid">Paid</SelectItem>
                              <SelectItem value="unpaid">Unpaid</SelectItem>
                              <SelectItem value="negotiable">Negotiable</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {form.watch("payment.type") === "paid" && (
                      <div className="grid grid-cols-2 gap-4">
                        <FormField
                          control={form.control}
                          name="payment.amount"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Amount</FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  placeholder="5000"
                                  {...field}
                                  onChange={(e) => field.onChange(Number(e.target.value))}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="payment.currency"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Currency</FormLabel>
                              <Select onValueChange={field.onChange} defaultValue={field.value}>
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="₹">₹ (INR)</SelectItem>
                                  <SelectItem value="$">$ (USD)</SelectItem>
                                  <SelectItem value="€">€ (EUR)</SelectItem>
                                  <SelectItem value="£">£ (GBP)</SelectItem>
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    )}
                  </div>

                  {/* Requirements */}
                  <div className="space-y-3">
                    <FormLabel>Requirements</FormLabel>
                    <div className="flex flex-wrap gap-2 mb-3">
                      {requirements.map((requirement) => (
                        <Badge key={requirement} variant="outline" className="gap-1">
                          {requirement}
                          <X className="w-3 h-3 cursor-pointer" onClick={() => removeRequirement(requirement)} />
                        </Badge>
                      ))}
                    </div>
                    <div className="flex gap-2">
                      <Input
                        placeholder="Add a requirement"
                        value={newRequirement}
                        onChange={(e) => setNewRequirement(e.target.value)}
                        onKeyPress={(e) => e.key === "Enter" && (e.preventDefault(), addRequirement())}
                      />
                      <Button type="button" variant="outline" onClick={addRequirement}>
                        <Plus className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>

                  {/* Associated Project */}
                  <FormField
                    control={form.control}
                    name="associatedProject"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Associated Project (Optional)</FormLabel>
                        <FormControl>
                          <Input placeholder="Link to album, song, or artist" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="flex gap-4 pt-6">
                    <Button type="submit" className="flex-1">
                      Post Opportunity
                    </Button>
                    <Link href="/opportunities">
                      <Button type="button" variant="outline">
                        Cancel
                      </Button>
                    </Link>
                  </div>
                </form>
              </Form>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
