"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Eye, MessageCircle, Calendar, Music } from "lucide-react"
import Link from "next/link"
import type { Application } from "../../features/opportunites/types"

// Mock applications data
const mockApplications: Application[] = [
  {
    id: "1",
    opportunityId: "1",
    opportunity: {
      id: "1",
      title: "Session Guitarist for Indie Rock EP",
      role: "Guitarist",
      description: "",
      postedBy: {
        id: "2",
        name: "The Midnight Band",
        type: "band",
        avatar: "/placeholder.svg?height=40&width=40",
      },
      genre: ["Indie", "Rock"],
      timeline: { startDate: "2024-07-01", endDate: "2024-07-31" },
      location: { type: "remote" },
      payment: { type: "paid", amount: 5000, currency: "₹" },
      requirements: [],
      status: "open",
      applicationsCount: 8,
      createdAt: "2024-01-10",
      updatedAt: "2024-01-10",
    },
    applicantId: "current-user",
    applicant: {
      id: "current-user",
      name: "You",
    },
    status: "pending",
    message: "I'm very interested in this project and have experience with indie rock...",
    appliedAt: "2024-01-15",
  },
  {
    id: "2",
    opportunityId: "2",
    opportunity: {
      id: "2",
      title: "Jazz Drummer for Album Recording",
      role: "Drummer",
      description: "",
      postedBy: {
        id: "3",
        name: "Reena Saini",
        type: "artist",
        avatar: "/placeholder.svg?height=40&width=40",
      },
      genre: ["Jazz"],
      timeline: { startDate: "2024-06-15", endDate: "2024-07-30" },
      location: { type: "remote" },
      payment: { type: "paid", amount: 8000, currency: "₹" },
      requirements: [],
      status: "open",
      applicationsCount: 12,
      createdAt: "2024-01-12",
      updatedAt: "2024-01-12",
    },
    applicantId: "current-user",
    applicant: {
      id: "current-user",
      name: "You",
    },
    status: "accepted",
    message: "I have 8 years of jazz drumming experience and would love to contribute...",
    appliedAt: "2024-01-13",
  },
  {
    id: "3",
    opportunityId: "3",
    opportunity: {
      id: "3",
      title: "Vocalist for Electronic Project",
      role: "Vocalist",
      description: "",
      postedBy: {
        id: "4",
        name: "Alex Producer",
        type: "person",
        avatar: "/placeholder.svg?height=40&width=40",
      },
      genre: ["Electronic"],
      timeline: { startDate: "2024-08-01", endDate: "2024-09-15" },
      location: { type: "hybrid" },
      payment: { type: "negotiable" },
      requirements: [],
      status: "open",
      applicationsCount: 15,
      createdAt: "2024-01-14",
      updatedAt: "2024-01-14",
    },
    applicantId: "current-user",
    applicant: {
      id: "current-user",
      name: "You",
    },
    status: "rejected",
    message: "I'm a versatile vocalist with experience in electronic music...",
    appliedAt: "2024-01-16",
  },
]

export default function ApplicationsPage() {
  const [statusFilter, setStatusFilter] = useState<string>("")

  const filteredApplications = mockApplications.filter((application) => {
    return !statusFilter || application.status === statusFilter
  })

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "pending":
        return <Badge variant="secondary">Pending</Badge>
      case "accepted":
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Accepted</Badge>
      case "rejected":
        return <Badge variant="destructive">Rejected</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="px-4 md:px-8 lg:px-16 py-8">
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-8">
          <div>
            <h1 className="text-3xl font-bold">My Applications</h1>
            <p className="text-muted-foreground">Track your opportunity applications</p>
          </div>
          <Link href="/opportunities">
            <Button>Browse Opportunities</Button>
          </Link>
        </div>

        {/* Filters */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="flex items-center gap-4">
              <span className="text-sm font-medium">Filter by status:</span>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="All statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All statuses</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="accepted">Accepted</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Applications List */}
        <div className="space-y-4">
          {filteredApplications.map((application) => (
            <Card key={application.id} className="hover:shadow-md transition-shadow">
              <CardContent className="pt-6">
                <div className="flex flex-col lg:flex-row gap-4">
                  <div className="flex-1">
                    <div className="flex items-start gap-4 mb-4">
                      <Avatar className="w-12 h-12">
                        <AvatarImage src={application.opportunity.postedBy.avatar || "/placeholder.svg"} />
                        <AvatarFallback>
                          {application.opportunity.postedBy.name
                            .split(" ")
                            .map((n) => n[0])
                            .join("")}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <h3 className="text-xl font-semibold mb-1">{application.opportunity.title}</h3>
                        <p className="text-muted-foreground text-sm mb-2">
                          Posted by {application.opportunity.postedBy.name}
                        </p>
                        <div className="flex flex-wrap gap-2 mb-3">
                          {application.opportunity.genre.map((genre) => (
                            <Badge key={genre} variant="secondary">
                              {genre}
                            </Badge>
                          ))}
                          {getStatusBadge(application.status)}
                        </div>
                      </div>
                    </div>

                    <div className="bg-muted/50 p-4 rounded-lg mb-4">
                      <p className="text-sm font-medium mb-2">Your application message:</p>
                      <p className="text-sm text-muted-foreground line-clamp-2">{application.message}</p>
                    </div>

                    <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <Calendar className="w-4 h-4" />
                        <span>Applied on {new Date(application.appliedAt).toLocaleDateString()}</span>
                      </div>
                      {application.opportunity.payment.type === "paid" && (
                        <div className="flex items-center gap-1">
                          <span>
                            {application.opportunity.payment.currency}
                            {application.opportunity.payment.amount?.toLocaleString()}
                          </span>
                        </div>
                      )}
                      {application.opportunity.associatedProject && (
                        <div className="flex items-center gap-1">
                          <Music className="w-4 h-4" />
                          <span>{application.opportunity.associatedProject.name}</span>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="flex flex-col gap-2 lg:w-auto w-full">
                    <Link href={`/opportunities/${application.opportunity.id}`}>
                      <Button variant="outline" className="w-full gap-2">
                        <Eye className="w-4 h-4" />
                        View Opportunity
                      </Button>
                    </Link>
                    {application.status === "accepted" && (
                      <Button className="w-full gap-2">
                        <MessageCircle className="w-4 h-4" />
                        Message Poster
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredApplications.length === 0 && (
          <Card className="text-center py-12">
            <CardContent>
              <Music className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No applications found</h3>
              <p className="text-muted-foreground mb-4">
                {statusFilter
                  ? `No applications with status "${statusFilter}"`
                  : "You haven't applied to any opportunities yet."}
              </p>
              <Link href="/opportunities">
                <Button>Browse Opportunities</Button>
              </Link>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
