'use client'

import { useSearchParams } from 'next/navigation'
import { Suspense } from 'react'
import AlbumPage from "@/features/album/components/album-page"

function AlbumContent() {
  const searchParams = useSearchParams()
  const albumId = searchParams.get('id')

  if (!albumId) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Album Not Found</h1>
          <p className="text-muted-foreground">Please provide a valid album ID in the URL.</p>
          <p className="text-sm text-muted-foreground mt-2">
            Example: /album?id=your-album-id
          </p>
        </div>
      </div>
    )
  }

  return <AlbumPage albumId={albumId} />
}

export default function Album() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading album...</p>
        </div>
      </div>
    }>
      <AlbumContent />
    </Suspense>
  )
}
