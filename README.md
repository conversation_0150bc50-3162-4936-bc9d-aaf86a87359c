# Smash Music 🎵

A modern music application built with Next.js, featuring dark/light theme support and internationalization.

## 🚀 Tech Stack

### Core Framework
- **[Next.js 15.3.3](https://nextjs.org)** - React framework with App Router and Turbopack
- **[React 19](https://react.dev)** - UI library with latest features
- **[TypeScript 5](https://www.typescriptlang.org)** - Type safety and developer experience

### Styling & UI
- **[Tailwind CSS v4](https://tailwindcss.com)** - Utility-first CSS framework
- **[shadcn/ui](https://ui.shadcn.com)** - Re-usable component library (New York style)
- **[Radix UI](https://www.radix-ui.com)** - Headless UI primitives for accessibility
- **[Lucide React](https://lucide.dev)** - Beautiful & consistent icon library
- **[Class Variance Authority](https://cva.style)** - Component variant management

### Features & Functionality
- **[next-intl](https://next-intl-docs.vercel.app)** - Internationalization without routing
- **Custom Theme System** - Dark/light mode with system preference detection
- **[<PERSON><PERSON><PERSON>ont](https://vercel.com/font)** - Modern typography by Vercel

### Development Tools
- **[ESLint](https://eslint.org)** - Code linting with Next.js config
- **[pnpm](https://pnpm.io)** - Fast, disk space efficient package manager
- **[PostCSS](https://postcss.org)** - CSS processing

## 📁 Project Structure

```
smash-music/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── globals.css        # Global styles and CSS variables
│   │   ├── layout.tsx         # Root layout with providers
│   │   ├── page.tsx          # Home page
│   │   └── favicon.ico       # App icon
│   ├── components/            # Reusable components
│   │   ├── ui/               # shadcn/ui components
│   │   │   ├── avatar.tsx
│   │   │   ├── badge.tsx
│   │   │   ├── button.tsx
│   │   │   ├── card.tsx
│   │   │   ├── select.tsx
│   │   │   └── index.ts
│   │   └── shared/           # Custom shared components
│   │       ├── theme/        # Theme-related components
│   │       │   └── ThemeToggle.tsx
│   │       └── language/     # Language-related components
│   │           └── LanguageToggle.tsx
│   ├── contexts/             # React contexts
│   │   ├── theme/           # Theme management
│   │   │   └── ThemeContext.tsx
│   ├── features/             # Feature-based architecture
│   │   └── home/            # Home page feature
│   │       ├── components/  # Home-specific components
│   │       │   ├── HomePage.tsx
│   │       │   └── index.ts
│   │       ├── hooks/       # Home-specific hooks
│   │       │   └── index.ts
│   │       ├── types/       # Home-specific types
│   │       │   └── index.ts
│   │       └── utils/       # Home-specific utilities
│   │           └── index.ts
│   ├── hooks/               # Shared hooks
│   │   └── index.ts
│   ├── i18n/               # Internationalization
│   │   ├── config.ts       # i18n configuration
│   │   ├── locale.ts       # Locale management
│   │   └── request.ts      # i18n request handling
│   └── lib/                # Shared utilities
│       └── utils.ts        # Common utility functions
├── messages/               # Translation files
│   ├── en.json            # English translations
│   ├── es.json            # Spanish translations
│   └── fr.json            # French translations
├── public/                # Static assets
│   ├── *.svg             # SVG icons and images
│   └── favicon.ico       # Favicon
├── components.json        # shadcn/ui configuration
├── tailwind.config.ts     # Tailwind CSS configuration
├── next.config.ts         # Next.js configuration
├── tsconfig.json         # TypeScript configuration
├── eslint.config.mjs     # ESLint configuration
├── postcss.config.mjs    # PostCSS configuration
├── package.json          # Dependencies and scripts
└── pnpm-lock.yaml       # pnpm lockfile
```

## 🛠️ Getting Started

### Prerequisites
- **Node.js 18+** - JavaScript runtime
- **pnpm** (recommended) - Package manager

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd smash-music
   ```

2. **Install dependencies**
   ```bash
   pnpm install
   ```

3. **Run the development server**
   ```bash
   pnpm dev
   ```

4. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000) to see the application.

### Available Scripts

```bash
pnpm dev          # Start development server with Turbopack
pnpm build        # Build for production
pnpm start        # Start production server
pnpm lint         # Run ESLint
```

## 🎨 Theme System

The application includes a comprehensive theme system with:

- **Light/Dark mode toggle** - Switch between themes
- **System preference detection** - Automatically detects user's system theme
- **Persistent theme storage** - Remembers user's theme choice
- **CSS custom properties** - Consistent theming across components
- **Tailwind CSS integration** - Uses `dark:` variants for styling

## 🌍 Internationalization

Built-in i18n support with next-intl:

- **Multiple languages**: English, Spanish, French
- **Type-safe translations** - Full TypeScript support
- **Easy to extend** - Add new languages by creating message files

### Supported Languages

| Language | Code | File |
|----------|------|------|
| English  | `en` | `messages/en.json` |
| Spanish  | `es` | `messages/es.json` |
| French   | `fr` | `messages/fr.json` |

### Adding Translations

1. **Add translations to message files**
   ```json
   // messages/en.json
   {
     "HomePage": {
       "title": "Smash Music",
       "subtitle": "A modern music application"
     }
   }
   ```

2. **Use translations in components**
   ```tsx
   import { useTranslations } from 'next-intl';

   function MyComponent() {
     const t = useTranslations('HomePage');
     return <h1>{t('title')}</h1>;
   }
   ```

## 🧩 Component Architecture

### shadcn/ui Integration

The project uses shadcn/ui components with:
- **New York style** variant for modern aesthetics
- **CSS variables** for consistent theming
- **Radix UI primitives** for accessibility
- **Lucide icons** for consistent iconography
- **Full TypeScript support** with proper typing

### Adding New shadcn/ui Components

```bash
# Add a new shadcn/ui component
npx shadcn@latest add [component-name]

# Example: Add a dialog component
npx shadcn@latest add dialog
```

### Component Organization

- **`src/components/ui/`** - shadcn/ui components (auto-generated)
- **`src/components/shared/`** - Custom reusable components
- **`src/features/[feature]/components/`** - Feature-specific components

## 📦 Features-Based Architecture

This project uses a **features-based architecture** for scalability and maintainability. Each feature/page has its own dedicated folder with all related code.

### Feature Structure

```
src/features/[feature-name]/
├── components/           # Feature-specific components
│   ├── FeatureComponent.tsx
│   └── index.ts
├── hooks/               # Feature-specific hooks
│   ├── useFeature.ts
│   └── index.ts
├── types/               # Feature-specific types
│   ├── feature.types.ts
│   └── index.ts
└── utils/               # Feature-specific utilities
    ├── feature.utils.ts
    └── index.ts
```

### Benefits

- **Scalability** - Easy to add new features without affecting existing code
- **Maintainability** - Related code is co-located
- **Team collaboration** - Multiple developers can work on different features
- **Code organization** - Clear separation of concerns

## 🔧 Development Guidelines

### Code Organization
- Use **feature-based architecture** for page-specific logic
- Keep **shared components** in `src/components/shared/`
- Place **reusable utilities** in `src/lib/`
- Follow **TypeScript best practices** with proper typing

### Styling Guidelines
- Use **Tailwind CSS** for all styling
- Leverage **CSS custom properties** for theming
- Prefer **shadcn/ui components** for consistency
- Use **dark:** variants for theme support

### State Management
- Use **React Context** for global state (theme, language)
- Implement **custom hooks** for feature-specific logic
- Keep **component state local** when possible
- Follow **React best practices** for state updates

## 🤝 Contributing

1. **Fork the repository**
2. **Create a feature branch** (`git checkout -b feature/amazing-feature`)
3. **Commit your changes** (`git commit -m 'Add amazing feature'`)
4. **Push to the branch** (`git push origin feature/amazing-feature`)
5. **Open a Pull Request**

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
